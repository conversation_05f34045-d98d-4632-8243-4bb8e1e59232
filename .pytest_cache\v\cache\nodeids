["test/test_fixes_validation.py::test_caching_mechanism", "test/test_fixes_validation.py::test_market_data_validation", "test/test_fixes_validation.py::test_pivot_point_calculation", "test/test_main_functionality.py::test_market_scanners", "test/test_main_functionality.py::test_options_filtering", "test/test_main_functionality.py::test_symbol_parsing", "test/test_main_functionality.py::test_unified_scanner_dry_run", "test/test_main_integration.py::test_main_dry_run", "test/test_main_integration.py::test_market_data_validation", "test/test_main_integration.py::test_pivot_point_integration", "test/test_options_prefiltering.py::test_options_prefiltering", "test/test_pivot_calculation_types.py::test_pivot_calculation_types", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_handles_no_spot_price", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_preserves_ce_pe_balance", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_reduces_symbols_correctly", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_respects_strike_level_config", "test/test_prefiltering_strike_levels.py::TestPrefilteringStrikeLevels::test_prefiltering_with_empty_symbols", "test/test_timeframe.py::test_fetch_intraday_data"]