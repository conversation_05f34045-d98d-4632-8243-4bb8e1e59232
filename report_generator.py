"""
Report generator for creating CSV reports of filtered symbols.
Generates reports with specified columns and formatting.
"""

import csv
import os
import logging
import re
from datetime import datetime
from typing import List, Dict, Any
from pathlib import Path

from market_type_scanner import FilteredSymbol
from options_chain_filter import OptionsChainFilter

logger = logging.getLogger(__name__)

class ReportGenerator:
    """Generator for creating CSV reports of scan results."""
    
    def __init__(self, output_dir: str = "reports", config=None):
        """
        Initialize the report generator.
        
        Args:
            output_dir: Directory to save reports
            config: ConfigLoader object (optional, required for EMA logic)
        """
        self.output_dir = output_dir
        self.ensure_output_directory()
        self.config = config
        
        # Get configured symbols for dynamic pattern matching
        if config and hasattr(config, 'symbols'):
            self.target_symbols = set(config.symbols)
        else:
            # Fallback to default symbols for backward compatibility
            self.target_symbols = {'NIFTY', 'BANKNIFTY'}
        
        # Create dynamic pattern for underlying extraction
        self._create_underlying_pattern()
    
    def _create_underlying_pattern(self) -> None:
        """
        Create dynamic regex pattern for extracting underlying symbols.
        """
        if not self.target_symbols:
            raise ValueError("No target symbols configured")
        
        # Escape special regex characters in symbol names and create alternation
        escaped_symbols = [re.escape(symbol) for symbol in sorted(self.target_symbols)]
        symbols_pattern = '|'.join(escaped_symbols)
        
        # Create pattern to match underlying at the start of symbol
        self.underlying_pattern = rf'^({symbols_pattern})'
        
        logger.debug(f"Created underlying extraction pattern: {self.underlying_pattern}")
        
    def ensure_output_directory(self) -> None:
        """Ensure the output directory exists."""
        try:
            Path(self.output_dir).mkdir(parents=True, exist_ok=True)
            logger.info(f"Output directory ready: {self.output_dir}")
        except Exception as e:
            logger.error(f"Failed to create output directory {self.output_dir}: {e}")
            raise

    def extract_underlying_from_symbol(self, symbol_str: str) -> str:
        """
        Extract underlying symbol from symbol string using configured symbols.

        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'

        Returns:
            Underlying symbol (e.g., 'NIFTY', 'BANKNIFTY', 'RELIANCE') or 'UNKNOWN'
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Extract underlying using dynamic pattern
            match = re.match(self.underlying_pattern, clean_symbol)

            if match:
                return match.group(1)
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting underlying from {symbol_str}: {e}")
            return "UNKNOWN"

    def extract_month_from_symbol(self, symbol_str: str) -> str:
        """
        Extract month from symbol string using configured symbols.

        Args:
            symbol_str: Symbol string like 'NSE:NIFTY25JUL57000CE'

        Returns:
            Month abbreviation like 'JUL'
        """
        try:
            # Remove NSE: prefix if present
            clean_symbol = symbol_str.replace('NSE:', '')

            # Create dynamic pattern for month extraction
            escaped_symbols = [re.escape(symbol) for symbol in sorted(self.target_symbols)]
            symbols_pattern = '|'.join(escaped_symbols)
            pattern = rf'^({symbols_pattern})(\d{{2}})([A-Z]{{3}})'
            
            match = re.match(pattern, clean_symbol)

            if match:
                return match.group(3)
            else:
                return "UNKNOWN"

        except Exception as e:
            logger.debug(f"Error extracting month from {symbol_str}: {e}")
            return "UNKNOWN"

    def sort_symbols_by_underlying_and_month(self, filtered_symbols: List[FilteredSymbol]) -> List[FilteredSymbol]:
        """
        Sort symbols by underlying (NIFTY first, then BANKNIFTY), month sequence, and preserve CE/PE pairs.
        This method preserves the original CE/PE pairing created by the pairing filter.

        Args:
            filtered_symbols: List of FilteredSymbol objects

        Returns:
            Sorted list of FilteredSymbol objects with original CE/PE pairs preserved
        """
        # For non-options market types, just sort by underlying and symbol
        if not filtered_symbols or filtered_symbols[0].market_type != 'OPTIONS':
            return sorted(filtered_symbols, key=lambda x: (x.underlying, x.symbol))

        # Define month order for sorting (for options)
        month_order = {
            'JUL': 1, 'AUG': 2, 'SEP': 3, 'OCT': 4, 'NOV': 5, 'DEC': 6,
            'JAN': 7, 'FEB': 8, 'MAR': 9, 'APR': 10, 'MAY': 11, 'JUN': 12
        }

        # Define underlying order (NIFTY first, then BANKNIFTY)
        underlying_order = {'NIFTY': 1, 'BANKNIFTY': 2, 'FINNIFTY': 3}

        # Group symbols by pair_id to preserve original pairing
        pair_groups = {}
        unpaired_symbols = []

        for symbol in filtered_symbols:
            pair_id = getattr(symbol, 'pair_id', None) if hasattr(symbol, 'pair_id') else None
            if pair_id:
                if pair_id not in pair_groups:
                    pair_groups[pair_id] = []
                pair_groups[pair_id].append(symbol)
            else:
                unpaired_symbols.append(symbol)

        # Create sorted pairs list
        sorted_pairs = []

        # Sort pairs by underlying and month
        for pair_id, pair_symbols in pair_groups.items():
            if len(pair_symbols) == 2:  # Valid CE/PE pair
                # Sort within pair: CE first, then PE
                pair_symbols.sort(key=lambda s: (s.option_type != 'CE', s.market_data.ltp))

                # Extract underlying and month for sorting
                underlying = pair_symbols[0].underlying
                month = pair_symbols[0].expiry_month if pair_symbols[0].expiry_month else 'UNKNOWN'

                # Add sort key to the pair
                sort_key = (
                    underlying_order.get(underlying, 999),
                    month_order.get(month, 999),
                    pair_symbols[0].market_data.ltp  # Use CE LTP for secondary sorting
                )
                sorted_pairs.append((sort_key, pair_symbols))
            else:
                logger.warning(f"Invalid pair {pair_id}: contains {len(pair_symbols)} symbols instead of 2")
                unpaired_symbols.extend(pair_symbols)

        # Sort pairs by the sort key
        sorted_pairs.sort(key=lambda x: x[0])

        # Build final sorted list
        sorted_symbols = []
        for _, pair_symbols in sorted_pairs:
            sorted_symbols.extend(pair_symbols)

        # Add any unpaired symbols at the end (shouldn't happen if pairing filter worked correctly)
        if unpaired_symbols:
            logger.warning(f"Found {len(unpaired_symbols)} unpaired symbols")
            unpaired_symbols.sort(key=lambda s: (
                underlying_order.get(s.underlying, 999),
                month_order.get(s.expiry_month if s.expiry_month else 'UNKNOWN', 999),
                s.option_type != 'CE' if s.option_type else True,
                s.market_data.ltp
            ))
            sorted_symbols.extend(unpaired_symbols)

        logger.info(f"Sorted {len(sorted_symbols)} symbols by underlying, month, and preserved CE/PE pairing")
        logger.info(f"Preserved {len(sorted_pairs)} CE/PE pairs, {len(unpaired_symbols)} unpaired symbols")
        return sorted_symbols
    
    def generate_filename(self, prefix: str = "index_scan") -> str:
        """
        Generate a timestamped filename for the report.
        
        Args:
            prefix: Prefix for the filename
            
        Returns:
            Generated filename with timestamp
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{prefix}_{timestamp}.csv"
        return os.path.join(self.output_dir, filename)
    
    def create_csv_report(self, filtered_symbols: List[FilteredSymbol],
                         filename: str = None) -> str:
        """
        Create a CSV report with filtered symbols.

        Args:
            filtered_symbols: List of FilteredSymbol objects
            filename: Optional custom filename

        Returns:
            Path to the created CSV file
        """
        if filename is None:
            filename = self.generate_filename()

        # Sort symbols by underlying and month sequence
        sorted_symbols = self.sort_symbols_by_underlying_and_month(filtered_symbols)

        # Conditionally add MAE column only
        mae_enabled = False
        pivot_enabled = False
        if self.config:
            mae_enabled = getattr(self.config, 'mae_enabled', False)
            pivot_enabled = getattr(self.config, 'pivot_point_enabled', False)

        # Determine if pair_id should be included based on config (ce_pe_pairing.enabled)
        pair_id_enabled = getattr(self.config, 'ce_pe_pairing_enabled', False)

        # Determine market type to decide on column structure
        market_type = sorted_symbols[0].market_type if sorted_symbols else 'UNKNOWN'
        is_options = market_type == 'OPTIONS'

        # Fetch spot prices for options if needed
        spot_prices = {}
        if is_options and self.config:
            try:
                options_filter = OptionsChainFilter(self.config)
                underlyings = list(set(symbol.underlying for symbol in sorted_symbols if hasattr(symbol, 'underlying')))
                spot_prices = options_filter.get_spot_prices_for_underlyings(underlyings)
                logger.info(f"Fetched spot prices for {len(spot_prices)} underlyings for CSV report")
            except Exception as e:
                logger.warning(f"Could not fetch spot prices for CSV report: {e}")
                spot_prices = {}

        headers = []
        if pair_id_enabled and is_options:
            headers.append('pair_id')

        # Include strike and expiry_date columns only for OPTIONS market type
        if is_options:
            headers += ['strike', 'expiry_date', 'spot_price']

        headers += [
            'type',
            'symbol',
            'LTP',
            'volume',
            'open',
            'high',
            'low',
            'close',
            'prev_close',
            'change',
            'change_percent',
        ]
        if mae_enabled:
            headers += ['mae_value', 'mae_type']

        if pivot_enabled:
            # Add delta column for options (same as pivot_point_core.py)
            if any(symbol.market_type == 'OPTIONS' for symbol in sorted_symbols):
                headers.append('delta')

            # Add minimum positive pivot columns first (same order as pivot_point_core.py)
            headers += [
                'min_positive_pivot_level', 'min_positive_pivot_value',
                'distance_to_min_positive_pivot', 'distance_to_min_positive_pivot_pct'
            ]

            # Add all pivot point columns (same as pivot_point_core.py)
            pivot_columns = ['Pivot', 'R1', 'S1', 'R2', 'S2', 'R3', 'S3', 'R4', 'S4', 'R5', 'S5', 'R6', 'S6', 'R7', 'S7', 'R8', 'S8', 'R9', 'S9', 'R10', 'S10', 'R11', 'S11']
            headers += pivot_columns

        try:
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # Write headers
                writer.writerow(headers)

                # Write data rows
                for symbol in sorted_symbols:
                    row = []

                    # Add pair_id only for OPTIONS market type
                    if pair_id_enabled and is_options:
                        row.append(getattr(symbol, 'pair_id', '') if hasattr(symbol, 'pair_id') else '')

                    # Handle different market types with different data structures
                    if symbol.market_type == 'OPTIONS':
                        # Get spot price for this underlying
                        underlying_spot_price = ''
                        if hasattr(symbol, 'underlying') and symbol.underlying in spot_prices:
                            underlying_spot_price = '{:.2f}'.format(spot_prices[symbol.underlying])
                        row += [
                            str(int(symbol.strike_price)) if symbol.strike_price else '',
                            self._format_expiry_date_for_pivot_mode(symbol, pivot_enabled),
                            underlying_spot_price,
                            symbol.option_type if symbol.option_type else '',
                            symbol.symbol,
                            '{:.2f}'.format(symbol.market_data.ltp),
                            symbol.market_data.volume,
                            '{:.2f}'.format(symbol.market_data.open_price),
                            '{:.2f}'.format(symbol.market_data.high_price),
                            '{:.2f}'.format(symbol.market_data.low_price),
                            '{:.2f}'.format(symbol.market_data.close_price),
                            '{:.2f}'.format(symbol.market_data.prev_close),
                            '{:.2f}'.format(symbol.market_data.change),
                            '{:.2f}'.format(symbol.market_data.change_percent),
                        ]
                    else:
                        # For EQUITY, INDEX, FUTURES - no strike/expiry columns
                        row += [
                            symbol.market_type,  # type
                            symbol.symbol,
                            '{:.2f}'.format(symbol.market_data.ltp),
                            symbol.market_data.volume,
                            '{:.2f}'.format(symbol.market_data.open_price),
                            '{:.2f}'.format(symbol.market_data.high_price),
                            '{:.2f}'.format(symbol.market_data.low_price),
                            '{:.2f}'.format(symbol.market_data.close_price),
                            '{:.2f}'.format(symbol.market_data.prev_close),
                            '{:.2f}'.format(symbol.market_data.change),
                            '{:.2f}'.format(symbol.market_data.change_percent),
                        ]

                    if mae_enabled:
                        # Determine which MAE type was used (default or smoothed)
                        mae_type = 'smoothed' if getattr(self.config, 'mae_smoothing_enabled', False) else 'default'
                        mae_value = getattr(symbol, 'mae_value', '') if hasattr(symbol, 'mae_value') else ''
                        row += [mae_value, mae_type]

                    if pivot_enabled:
                        # Add delta column for options (same as pivot_point_core.py)
                        if symbol.market_type == 'OPTIONS':
                            delta_value = getattr(symbol, 'delta', '') if hasattr(symbol, 'delta') else ''
                            logger.debug(f"Adding delta value '{delta_value}' for symbol {symbol.symbol} to CSV")
                            row.append(delta_value)

                        # Add pivot point data if available
                        pivot_data = getattr(symbol, 'pivot_data', None) if hasattr(symbol, 'pivot_data') else None
                        if pivot_data:
                            # Add minimum positive pivot columns first (same order as pivot_point_core.py)
                            row += [
                                pivot_data.min_positive_pivot_level or '',
                                '{:.2f}'.format(pivot_data.min_positive_pivot_value) if pivot_data.min_positive_pivot_value else '',
                                '{:.2f}'.format(pivot_data.distance_to_min_positive_pivot) if pivot_data.distance_to_min_positive_pivot else '',
                                '{:.2f}'.format(pivot_data.distance_to_min_positive_pivot_pct) if pivot_data.distance_to_min_positive_pivot_pct else ''
                            ]

                            # Add all pivot levels (same as pivot_point_core.py)
                            pivot_columns = ['Pivot', 'R1', 'S1', 'R2', 'S2', 'R3', 'S3', 'R4', 'S4', 'R5', 'S5', 'R6', 'S6', 'R7', 'S7', 'R8', 'S8', 'R9', 'S9', 'R10', 'S10', 'R11', 'S11']
                            for col in pivot_columns:
                                value = pivot_data.pivot_levels.get(col, '') if pivot_data.pivot_levels else ''
                                row.append('{:.2f}'.format(value) if isinstance(value, (int, float)) and value != '' else value)
                        else:
                            # Add empty values for all pivot columns
                            row += [''] * 4  # 4 min positive pivot columns
                            pivot_columns = ['Pivot', 'R1', 'S1', 'R2', 'S2', 'R3', 'S3', 'R4', 'S4', 'R5', 'S5', 'R6', 'S6', 'R7', 'S7', 'R8', 'S8', 'R9', 'S9', 'R10', 'S10', 'R11', 'S11']
                            row += [''] * len(pivot_columns)

                    writer.writerow(row)
            
            logger.info(f"CSV report created successfully: {filename}")
            logger.info(f"Report contains {len(filtered_symbols)} symbols")
            return filename
            
        except Exception as e:
            logger.error(f"Failed to create CSV report: {e}")
            raise
    
    def create_summary_report(self, filtered_symbols: List[FilteredSymbol],
                             summary_stats: Dict[str, Any],
                             filename: str = None) -> str:
        """
        Create a summary report with statistics.
        
        Args:
            filtered_symbols: List of FilteredSymbol objects
            summary_stats: Dictionary with summary statistics
            filename: Optional custom filename
        Returns:
            Path to the created summary file
        """
        if filename is None:
            base_filename = self.generate_filename("index_scan_summary")
            filename = base_filename.replace('.csv', '.txt')
        try:
            # Use only paired symbols (as in CSV)
            paired_symbols = self.sort_symbols_by_underlying_and_month(filtered_symbols)
            with open(filename, 'w', encoding='utf-8') as file:
                # Determine market type for report title
                market_type = paired_symbols[0].market_type if paired_symbols else "MARKET"
                file.write(f"{market_type} SCANNER SUMMARY REPORT\n")
                file.write("=" * 50 + "\n\n")

                # Timestamp
                file.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                # Summary statistics (recompute for paired symbols)
                file.write("SUMMARY STATISTICS:\n")
                file.write("-" * 20 + "\n")
                file.write(f"Total Symbols Found: {len(paired_symbols)}\n")

                if market_type == 'OPTIONS':
                    nifty_count = sum(1 for s in paired_symbols if 'NIFTY' in s.underlying and 'BANKNIFTY' not in s.underlying)
                    banknifty_count = sum(1 for s in paired_symbols if 'BANKNIFTY' in s.underlying)
                    ce_count = sum(1 for s in paired_symbols if s.option_type == 'CE')
                    pe_count = sum(1 for s in paired_symbols if s.option_type == 'PE')
                    file.write(f"NIFTY Options: {nifty_count}\n")
                    file.write(f"BANKNIFTY Options: {banknifty_count}\n")
                    file.write(f"Call Options (CE): {ce_count}\n")
                    file.write(f"Put Options (PE): {pe_count}\n\n")
                else:
                    # For other market types, show underlying distribution
                    underlying_counts = {}
                    for s in paired_symbols:
                        underlying_counts[s.underlying] = underlying_counts.get(s.underlying, 0) + 1
                    for underlying, count in sorted(underlying_counts.items()):
                        file.write(f"{underlying} Symbols: {count}\n")
                    file.write("\n")

                # Top symbols by volume
                if paired_symbols:
                    file.write("TOP 10 SYMBOLS BY VOLUME:\n")
                    file.write("-" * 30 + "\n")

                    # Sort by volume and take top 10
                    top_by_volume = sorted(paired_symbols, key=lambda x: x.market_data.volume, reverse=True)[:10]

                    for i, symbol in enumerate(top_by_volume, 1):
                        file.write(f"{i:2d}. {symbol.symbol:<30} Volume: {symbol.market_data.volume:>10,} LTP: {round(symbol.market_data.ltp, 2):>8.2f}\n")

                    file.write("\n")

                    # Top symbols by LTP
                    file.write("TOP 10 SYMBOLS BY LTP:\n")
                    file.write("-" * 25 + "\n")

                    top_by_ltp = sorted(paired_symbols, key=lambda x: x.market_data.ltp, reverse=True)[:10]

                    for i, symbol in enumerate(top_by_ltp, 1):
                        file.write(f"{i:2d}. {symbol.symbol:<30} LTP: {round(symbol.market_data.ltp, 2):>8.2f} Volume: {symbol.market_data.volume:>10,}\n")

                # --- Professional conclusion and unpaired symbol section ---
                # Identify unpaired symbols for optional debug section (only for OPTIONS)
                unpaired_symbols = []
                if market_type == 'OPTIONS':
                    base_groups = {}
                    for symbol in filtered_symbols:
                        underlying = symbol.underlying
                        year = symbol.expiry_year if symbol.expiry_year else '25'
                        month = symbol.expiry_month if symbol.expiry_month else 'UNKNOWN'
                        base = f"{underlying}{year}{month}"
                        if base not in base_groups:
                            base_groups[base] = []
                        base_groups[base].append(symbol)

                    for base, group_symbols in base_groups.items():
                        ce_symbols = [s for s in group_symbols if s.option_type == 'CE']
                        pe_symbols = [s for s in group_symbols if s.option_type == 'PE']
                        min_pairs = min(len(ce_symbols), len(pe_symbols))
                        # Unpaired CEs
                        if len(ce_symbols) > min_pairs:
                            unpaired_symbols.extend(ce_symbols[min_pairs:])
                        # Unpaired PEs
                        if len(pe_symbols) > min_pairs:
                            unpaired_symbols.extend(pe_symbols[min_pairs:])

                file.write("=" * 40 + "\n")
                file.write("REPORT GENERATION COMPLETE\n")
                file.write("=" * 40 + "\n")
                file.write("All statistics above reflect only valid CE/PE pairs.\n")
                if not paired_symbols:
                    file.write("No valid CE/PE pairs found. No symbols written to report.\n")
                file.write("\n")

                # Optional: List unpaired symbols for debugging
                if unpaired_symbols:
                    file.write("WARNING: The following symbols were not paired and are not included in the report:\n")
                    for symbol in unpaired_symbols:
                        option_type = symbol.option_type if symbol.option_type else symbol.market_type
                        volume = symbol.market_data.volume
                        ltp = '{:.2f}'.format(symbol.market_data.ltp)
                        file.write(f"  - {symbol.symbol} (Type: {option_type}, Volume: {volume}, LTP: {ltp})\n")
                    file.write("\n")

            logger.info(f"Summary report created successfully: {filename}")
            return filename
        except Exception as e:
            logger.error(f"Failed to create summary report: {e}")
            raise
    
    def print_console_summary(self, filtered_symbols: List[FilteredSymbol],
                            summary_stats: Dict[str, Any]) -> None:
        """
        Print a summary to console for immediate feedback.
        
        Args:
            filtered_symbols: List of FilteredSymbol objects
            summary_stats: Dictionary with summary statistics
        """
        market_type = summary_stats.get('market_type', 'MARKET')
        print("\n" + "=" * 60)
        print(f"{market_type} SCANNER RESULTS SUMMARY")
        print("=" * 60)

        print(f"Total Symbols Found: {summary_stats.get('total_symbols', 0)}")

        if market_type == 'OPTIONS':
            print(f"CE Options: {summary_stats.get('ce_options', 0)}")
            print(f"PE Options: {summary_stats.get('pe_options', 0)}")

        print(f"Unique Underlyings: {summary_stats.get('unique_underlyings', 0)}")

        if filtered_symbols:
            print("\nTOP 5 SYMBOLS BY VOLUME:")
            print("-" * 40)

            top_by_volume = sorted(filtered_symbols, key=lambda x: x.market_data.volume, reverse=True)[:5]

            for i, symbol in enumerate(top_by_volume, 1):
                print(f"{i}. {symbol.symbol} - Volume: {symbol.market_data.volume:,}, LTP: {symbol.market_data.ltp:.2f}")
        
        print("=" * 60 + "\n")
    
    def generate_market_type_report(self, filtered_symbols: List[FilteredSymbol],
                                  summary_stats: Dict[str, Any],
                                  market_type: str) -> Dict[str, str]:
        """
        Generate reports for a specific market type.

        Args:
            filtered_symbols: List of filtered symbols
            summary_stats: Summary statistics
            market_type: Market type (EQUITY, INDEX, FUTURES, OPTIONS)

        Returns:
            Dictionary with report file paths
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Generate CSV report
        csv_filename = f"{market_type.lower()}_scan_{timestamp}.csv"
        csv_filepath = os.path.join(self.output_dir, csv_filename)
        self.create_csv_report(filtered_symbols, csv_filepath)

        # Generate summary report
        summary_filename = f"{market_type.lower()}_scan_summary_{timestamp}.txt"
        summary_filepath = os.path.join(self.output_dir, summary_filename)
        self.create_summary_report(filtered_symbols, summary_stats, summary_filepath)

        logger.info(f"Generated {market_type} reports:")
        logger.info(f"  CSV: {csv_filepath}")
        logger.info(f"  Summary: {summary_filepath}")

        return {
            'csv_report': csv_filepath,
            'summary_report': summary_filepath
        }

    def generate_full_report(self, filtered_symbols: List[FilteredSymbol],
                           summary_stats: Dict[str, Any]) -> Dict[str, str]:
        """
        Generate both CSV and summary reports.
        
        Args:
            filtered_symbols: List of FilteredSymbol objects
            summary_stats: Dictionary with summary statistics
            
        Returns:
            Dictionary with paths to created files
        """
        try:
            # Generate CSV report
            csv_file = self.create_csv_report(filtered_symbols)
            
            # Generate summary report
            summary_file = self.create_summary_report(filtered_symbols, summary_stats)
            
            # Print console summary
            self.print_console_summary(filtered_symbols, summary_stats)
            
            return {
                'csv_report': csv_file,
                'summary_report': summary_file
            }
            
        except Exception as e:
            logger.error(f"Failed to generate full report: {e}")
            raise

    def _format_expiry_date_for_pivot_mode(self, symbol, pivot_enabled: bool) -> str:
        """Format expiry date to match pivot_point_core.py format when pivot points are enabled."""
        if pivot_enabled and symbol.market_type == 'OPTIONS':
            # Use full date format like pivot_point_core.py (2025-07-31)
            if hasattr(symbol, 'expiry_date') and symbol.expiry_date:
                return symbol.expiry_date.strftime('%Y-%m-%d') if hasattr(symbol.expiry_date, 'strftime') else str(symbol.expiry_date)
            elif symbol.expiry_year and symbol.expiry_month:
                import calendar
                from datetime import datetime, timedelta
                try:
                    year = int(symbol.expiry_year)
                    month_name = str(symbol.expiry_month).upper()
                    month_map = {
                        'JAN': 1, 'FEB': 2, 'MAR': 3, 'APR': 4, 'MAY': 5, 'JUN': 6,
                        'JUL': 7, 'AUG': 8, 'SEP': 9, 'OCT': 10, 'NOV': 11, 'DEC': 12
                    }
                    month = month_map.get(month_name, 1)
                    last_day = calendar.monthrange(year, month)[1]
                    last_date = datetime(year, month, last_day)
                    days_back = (last_date.weekday() - 3) % 7
                    if days_back == 0 and last_date.weekday() != 3:
                        days_back = 7
                    last_thursday = last_date - timedelta(days=days_back)
                    return last_thursday.strftime('%Y-%m-%d')
                except Exception:
                    return ''
            else:
                return ''
        else:
            # Original format for non-pivot point mode
            if symbol.expiry_year and symbol.expiry_month:
                try:
                    expiry_year = int(symbol.expiry_year)
                    expiry_month = str(symbol.expiry_month)
                    return f"{expiry_month}{expiry_year % 100:02d}"
                except (ValueError, TypeError, AttributeError):
                    return ""
            else:
                return ''
