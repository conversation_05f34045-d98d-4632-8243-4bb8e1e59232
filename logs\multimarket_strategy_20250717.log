root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-17 17:27:31
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-17 17:27:31
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - 
=== Fyers API Authentication ===
fyers_config - INFO - A browser window will open for you to log in to Fyers.
fyers_config - INFO - After logging in, you will be redirected to Google.
fyers_config - INFO - Copy the auth code from the URL and paste it here.
fyers_config - INFO - 
Please login in the private browser window that opened.
fyers_config - INFO - Authentication files saved to C:\Users\<USER>\Desktop\Python\simple_hft
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
fyers_connect - INFO - Weekly OHLC for DIXON: O=15240, H=15930, L=15118, C=15786
fyers_connect - INFO - Weekly OHLC for MCX: O=8888, H=8920.0, L=8022.5, C=8048.0
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250717_172842.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250717_172842.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250717_172842.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250717_172842.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250717_172842.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250717_172842.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.7s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
fyers_connect - INFO - Weekly OHLC for DIXON25JUL: O=15269, H=15980, L=15156, C=15810
fyers_connect - INFO - Weekly OHLC for MCX25JUL: O=8947.5, H=8968.0, L=8047.0, C=8073.0
fyers_connect - INFO - Weekly OHLC for DIXON25AUG: O=15319, H=16050, L=15250, C=15879
fyers_connect - INFO - Weekly OHLC for MCX25AUG: O=8915.0, H=8978.5, L=8066.0, C=8087.5
fyers_connect - INFO - Weekly OHLC for DIXON25SEP: O=15400, H=16100, L=15359, C=15946
fyers_connect - INFO - Weekly OHLC for MCX25SEP: O=8981.5, H=9022.5, L=8109.0, C=8132.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250717_172848.csv
report_generator - INFO - Report contains 5 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250717_172848.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250717_172848.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250717_172848.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 5
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250717_172848.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250717_172848.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.038356164383561646, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250717_172859.csv
report_generator - INFO - Report contains 5 symbols
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250717_172859.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250717_172859.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250717_172859.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 5
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250717_172859.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250717_172859.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250717_172859.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-17 17:28:59
unified_scanner - INFO - Total symbols found: 12
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 5
unified_scanner - INFO -   - OPTIONS symbols: 5
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250717_172859.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-17 19:56:14
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-17 19:56:14
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.8s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
fyers_connect - INFO - Weekly OHLC for DIXON: O=15240, H=15930, L=15118, C=15786
fyers_connect - INFO - Weekly OHLC for MCX: O=8888, H=8920.0, L=8022.5, C=8048.0
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250717_195621.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250717_195621.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250717_195621.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250717_195621.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250717_195621.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250717_195621.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.7s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
fyers_connect - INFO - Weekly OHLC for DIXON25JUL: O=15269, H=15980, L=15156, C=15810
fyers_connect - INFO - Weekly OHLC for MCX25JUL: O=8947.5, H=8968.0, L=8047.0, C=8073.0
fyers_connect - INFO - Weekly OHLC for DIXON25AUG: O=15319, H=16050, L=15250, C=15879
fyers_connect - INFO - Weekly OHLC for MCX25AUG: O=8915.0, H=8978.5, L=8066.0, C=8087.5
fyers_connect - INFO - Weekly OHLC for DIXON25SEP: O=15400, H=16100, L=15359, C=15946
fyers_connect - INFO - Weekly OHLC for MCX25SEP: O=8981.5, H=9022.5, L=8109.0, C=8132.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250717_195630.csv
report_generator - INFO - Report contains 5 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250717_195630.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250717_195630.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250717_195630.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 5
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250717_195630.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250717_195630.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.038356164383561646, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.8s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250717_195641.csv
report_generator - INFO - Report contains 5 symbols
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250717_195641.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250717_195641.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250717_195641.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 5
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250717_195641.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250717_195641.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250717_195643.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-17 19:56:43
unified_scanner - INFO - Total symbols found: 12
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 5
unified_scanner - INFO -   - OPTIONS symbols: 5
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250717_195643.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-17 20:04:49
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-17 20:04:49
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: MONTHLY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250717_200455.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250717_200455.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250717_200455.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250717_200455.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250717_200455.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250717_200455.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: MONTHLY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: MONTHLY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250717_200500.csv
report_generator - INFO - Report contains 5 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250717_200500.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250717_200500.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250717_200500.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 5
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250717_200500.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250717_200500.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: MONTHLY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.038356164383561646, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.5s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250717_200510.csv
report_generator - INFO - Report contains 5 symbols
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250717_200510.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250717_200510.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250717_200510.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 5
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250717_200510.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250717_200510.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250717_200511.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-17 20:05:11
unified_scanner - INFO - Total symbols found: 12
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 5
unified_scanner - INFO -   - OPTIONS symbols: 5
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250717_200511.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-17 20:11:33
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-17 20:11:33
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250717_201137.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250717_201137.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250717_201137.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250717_201137.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250717_201137.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250717_201137.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250717_201141.csv
report_generator - INFO - Report contains 5 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250717_201141.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250717_201141.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250717_201141.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 5
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250717_201141.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250717_201141.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.038356164383561646, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.7s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250717_201152.csv
report_generator - INFO - Report contains 5 symbols
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250717_201152.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250717_201152.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250717_201152.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 5
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250717_201152.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250717_201152.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250717_201153.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-17 20:11:53
unified_scanner - INFO - Total symbols found: 12
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 5
unified_scanner - INFO -   - OPTIONS symbols: 5
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250717_201153.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-17 23:31:47
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-17 23:31:47
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
fyers_connect - INFO - Weekly OHLC for DIXON: O=15240, H=15930, L=15118, C=15786
fyers_connect - INFO - Weekly OHLC for MCX: O=8888, H=8920.0, L=8022.5, C=8048.0
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250717_233151.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250717_233151.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250717_233151.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250717_233151.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250717_233151.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250717_233151.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
fyers_connect - INFO - Weekly OHLC for DIXON25JUL: O=15269, H=15980, L=15156, C=15810
fyers_connect - INFO - Weekly OHLC for MCX25JUL: O=8947.5, H=8968.0, L=8047.0, C=8073.0
fyers_connect - INFO - Weekly OHLC for DIXON25AUG: O=15319, H=16050, L=15250, C=15879
fyers_connect - INFO - Weekly OHLC for MCX25AUG: O=8915.0, H=8978.5, L=8066.0, C=8087.5
fyers_connect - INFO - Weekly OHLC for DIXON25SEP: O=15400, H=16100, L=15359, C=15946
fyers_connect - INFO - Weekly OHLC for MCX25SEP: O=8981.5, H=9022.5, L=8109.0, C=8132.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250717_233154.csv
report_generator - INFO - Report contains 5 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250717_233154.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250717_233154.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250717_233154.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 5
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250717_233154.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250717_233154.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.038356164383561646, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 5 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 5 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250717_233200.csv
report_generator - INFO - Report contains 5 symbols
report_generator - WARNING - Found 5 unpaired symbols
report_generator - INFO - Sorted 5 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 5 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250717_233200.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250717_233200.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250717_233200.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 5
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250717_233200.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250717_233200.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250717_233201.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-17 23:32:01
unified_scanner - INFO - Total symbols found: 12
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 5
unified_scanner - INFO -   - OPTIONS symbols: 5
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250717_233201.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-17 23:32:24
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-17 23:32:24
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
fyers_connect - INFO - Weekly OHLC for DIXON: O=15240, H=15930, L=15118, C=15786
fyers_connect - INFO - Weekly OHLC for MCX: O=8888, H=8920.0, L=8022.5, C=8048.0
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250717_233227.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250717_233227.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250717_233227.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250717_233227.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250717_233227.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250717_233227.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
fyers_connect - INFO - Weekly OHLC for DIXON25JUL: O=15269, H=15980, L=15156, C=15810
fyers_connect - INFO - Weekly OHLC for MCX25JUL: O=8947.5, H=8968.0, L=8047.0, C=8073.0
fyers_connect - INFO - Weekly OHLC for DIXON25AUG: O=15319, H=16050, L=15250, C=15879
fyers_connect - INFO - Weekly OHLC for MCX25AUG: O=8915.0, H=8978.5, L=8066.0, C=8087.5
fyers_connect - INFO - Weekly OHLC for DIXON25SEP: O=15400, H=16100, L=15359, C=15946
fyers_connect - INFO - Weekly OHLC for MCX25SEP: O=8981.5, H=9022.5, L=8109.0, C=8132.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 6 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250717_233230.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250717_233230.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250717_233230.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250717_233230.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250717_233230.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250717_233230.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: WEEKLY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.038356164383561646, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 10 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 10 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250717_233235.csv
report_generator - INFO - Report contains 10 symbols
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250717_233235.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250717_233235.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250717_233235.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 10
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250717_233235.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250717_233235.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250717_233236.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-17 23:32:36
unified_scanner - INFO - Total symbols found: 18
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 10
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250717_233236.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-17 23:35:47
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-17 23:35:47
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.4s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250717_233550.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250717_233550.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250717_233550.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250717_233550.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250717_233550.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250717_233550.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 6 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250717_233554.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250717_233554.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250717_233554.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250717_233554.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250717_233554.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250717_233554.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.038356164383561646, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 10 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 10 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250717_233558.csv
report_generator - INFO - Report contains 10 symbols
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250717_233558.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250717_233558.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250717_233558.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 10
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250717_233558.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250717_233558.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250717_233559.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-17 23:35:59
unified_scanner - INFO - Total symbols found: 18
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 10
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250717_233559.txt
root - INFO - ================================================================================
root - INFO - Starting new logging session
root - INFO - ================================================================================
fyers_config - INFO - ================================================================================
fyers_config - INFO - UNIFIED SCANNER APPLICATION STARTED
fyers_config - INFO - ================================================================================
fyers_config - INFO - Start time: 2025-07-17 23:38:24
config_loader - INFO - Configuration loaded successfully from C:\Users\<USER>\Desktop\Python\simple_hft\config.yaml
unified_scanner - INFO - Unified scanner initialized for market types: ['EQUITY', 'INDEX', 'FUTURES', 'OPTIONS']
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER APPLICATION STARTED
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - Start time: 2025-07-17 23:38:24
unified_scanner - INFO - Enabled market types: EQUITY, INDEX, FUTURES, OPTIONS
unified_scanner - INFO - Validating prerequisites...
config_loader - INFO - Configuration validation successful
unified_scanner - INFO - All prerequisites satisfied
unified_scanner - INFO - Downloading latest symbol files...
unified_scanner - INFO - Symbol files downloaded successfully
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING EQUITY SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized EQUITY scanner
market_type_scanner - INFO - Starting EQUITY scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 2 symbols from NSE_CM.csv for market types: ['EQUITY']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 2 valid symbols
universal_symbol_parser - INFO - Found 2 EQUITY symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 2 EQUITY symbols
fyers_client - INFO - Batch 1/1 (2 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 2/2 symbols (100.0% batch success rate) in 0.5s
market_type_scanner - INFO - Volume filter: 2/2 symbols passed
market_type_scanner - INFO - LTP filter: 2/2 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 2 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 2 symbols
pivot_point_integration - INFO - Filtered to top 2 symbols closest to minimum positive pivot points
market_type_scanner - INFO - EQUITY pivot point scanning completed: 2 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\equity_scan_20250717_233830.csv
report_generator - INFO - Report contains 2 symbols
report_generator - INFO - Summary report created successfully: reports\equity_scan_summary_20250717_233830.txt
report_generator - INFO - Generated EQUITY reports:
report_generator - INFO -   CSV: reports\equity_scan_20250717_233830.csv
report_generator - INFO -   Summary: reports\equity_scan_summary_20250717_233830.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - EQUITY symbols found: 2
unified_scanner - INFO - EQUITY CSV Report: reports\equity_scan_20250717_233830.csv
unified_scanner - INFO - EQUITY Summary Report: reports\equity_scan_summary_20250717_233830.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING INDEX SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized INDEX scanner
market_type_scanner - INFO - Starting INDEX scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 0 symbols from NSE_CM.csv for market types: ['INDEX']
universal_symbol_parser - INFO - Performance stats: Processed 8288 rows, found 0 valid symbols
universal_symbol_parser - INFO - Found 0 INDEX symbols for scanning
market_type_scanner - WARNING - No INDEX symbols found for pivot point scanning
unified_scanner - WARNING - No INDEX symbols found matching the criteria
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING FUTURES SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized FUTURES scanner
market_type_scanner - INFO - Starting FUTURES scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
universal_symbol_parser - INFO - Loaded 6 symbols from NSE_FO.csv for market types: ['FUTURES']
universal_symbol_parser - INFO - Performance stats: Processed 78328 rows, found 6 valid symbols
universal_symbol_parser - INFO - Found 6 FUTURES symbols for scanning
market_type_scanner - INFO - Pivot point mode: Fetching market data for 6 FUTURES symbols
fyers_client - INFO - Batch 1/1 (6 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 6/6 symbols (100.0% batch success rate) in 0.3s
market_type_scanner - INFO - Volume filter: 6/6 symbols passed
market_type_scanner - INFO - LTP filter: 6/6 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 6 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 6 symbols
pivot_point_integration - INFO - Filtered to top 6 symbols closest to minimum positive pivot points
market_type_scanner - INFO - FUTURES pivot point scanning completed: 6 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - INFO - CSV report created successfully: reports\futures_scan_20250717_233834.csv
report_generator - INFO - Report contains 6 symbols
report_generator - INFO - Summary report created successfully: reports\futures_scan_summary_20250717_233834.txt
report_generator - INFO - Generated FUTURES reports:
report_generator - INFO -   CSV: reports\futures_scan_20250717_233834.csv
report_generator - INFO -   Summary: reports\futures_scan_summary_20250717_233834.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - FUTURES symbols found: 6
unified_scanner - INFO - FUTURES CSV Report: reports\futures_scan_20250717_233834.csv
unified_scanner - INFO - FUTURES Summary Report: reports\futures_scan_summary_20250717_233834.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - STARTING OPTIONS SCANNING
unified_scanner - INFO - ============================================================
universal_symbol_parser - INFO - Universal symbol parser initialized for 2 target symbols
universal_symbol_parser - INFO - Use all symbols: False
market_type_scanner - INFO - Initialized OPTIONS scanner
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
market_type_scanner - INFO - Starting OPTIONS scanning...
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
pivot_point_integration - INFO - Pivot Point Integration initialized - Enabled: True, Type: DAILY
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Pivot point mode: Processing symbols: ['DIXON', 'MCX']
root - INFO - Generating config for new symbol 'DIXON' based on spot price.
fyers_connect - ERROR - Monthly option symbol NSE:DIXON25JUL15100CE is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
market_type_scanner - ERROR - Error processing symbol DIXON in pivot point mode: Monthly option symbol is invalid or returned no data. Tried symbol: NSE:DIXON25JUL15100CE for expiry_type: MONTHLY. If already using MONTHLY, this is likely a symbol or broker issue.
root - INFO - Generating config for new symbol 'MCX' based on spot price.
option_utils - WARNING - Could not find implied volatility for symbol=NSE:MCX25JUL7300CE, price=302.45, S=8259.5, K=7300.0, T=0.038356164383561646, r=0.065, option_type=CE: f(a) and f(b) must have different signs. Returning default IV.
market_type_scanner - INFO - Pivot point mode: Added 11 options for MCX
market_type_scanner - INFO - Pivot point mode: Total symbols selected: 11
market_type_scanner - INFO - Pivot point mode: Fetching market data for 11 OPTIONS symbols
fyers_client - INFO - Batch 1/1 (11 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 11/11 symbols (100.0% batch success rate) in 0.2s
market_type_scanner - INFO - Volume filter: 11/11 symbols passed
market_type_scanner - INFO - LTP filter: 11/11 symbols passed
market_type_scanner - INFO - Applying pivot point calculations to 11 symbols
fyers_connect - INFO - Starting Fyers authentication process...
fyers_config - INFO - Fyers authentication successful!
fyers_connect - INFO - Successfully logged in to Fyers API
fyers_connect - INFO - Spot price for DIXON: 16107.0
fyers_connect - INFO - Spot price for MCX: 8259.5
market_type_scanner - INFO - Calculated pivot points for 11 symbols
pivot_point_integration - INFO - Filtered to top 10 symbols closest to minimum positive pivot points
market_type_scanner - INFO - OPTIONS pivot point scanning completed: 10 symbols found
report_generator - INFO - Output directory ready: reports
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
options_chain_filter - INFO - Options chain filter initialized with strike level: 20
fyers_client - INFO - Starting Fyers authentication...
fyers_config - INFO - Fyers authentication successful!
fyers_client - INFO - Fyers authentication successful
options_chain_filter - INFO - Fetching spot prices for 1 underlyings...
fyers_client - INFO - Batch 1/1 (1 symbols) - Rate: 0.0 batches/sec, ETA: 0s
fyers_client - INFO - Quote fetching completed: 1/1 symbols (100.0% batch success rate) in 0.2s
options_chain_filter - INFO - Retrieved spot prices for 1/1 underlyings
report_generator - INFO - Fetched spot prices for 1 underlyings for CSV report
report_generator - INFO - CSV report created successfully: reports\options_scan_20250717_233839.csv
report_generator - INFO - Report contains 10 symbols
report_generator - WARNING - Found 10 unpaired symbols
report_generator - INFO - Sorted 10 symbols by underlying, month, and preserved CE/PE pairing
report_generator - INFO - Preserved 0 CE/PE pairs, 10 unpaired symbols
report_generator - INFO - Summary report created successfully: reports\options_scan_summary_20250717_233839.txt
report_generator - INFO - Generated OPTIONS reports:
report_generator - INFO -   CSV: reports\options_scan_20250717_233839.csv
report_generator - INFO -   Summary: reports\options_scan_summary_20250717_233839.txt
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS SCANNING COMPLETED
unified_scanner - INFO - ============================================================
unified_scanner - INFO - OPTIONS symbols found: 10
unified_scanner - INFO - OPTIONS CSV Report: reports\options_scan_20250717_233839.csv
unified_scanner - INFO - OPTIONS Summary Report: reports\options_scan_summary_20250717_233839.txt
unified_scanner - INFO - Combined summary report generated: reports\combined_scan_summary_20250717_233840.txt
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - UNIFIED SCANNER COMPLETED SUCCESSFULLY
unified_scanner - INFO - ================================================================================
unified_scanner - INFO - End time: 2025-07-17 23:38:40
unified_scanner - INFO - Total symbols found: 18
unified_scanner - INFO -   - EQUITY symbols: 2
unified_scanner - INFO -   - INDEX symbols: 0
unified_scanner - INFO -   - FUTURES symbols: 6
unified_scanner - INFO -   - OPTIONS symbols: 10
unified_scanner - INFO - Combined summary: reports\combined_scan_summary_20250717_233840.txt
