import logging
import pandas as pd
import os
from datetime import datetime
from fyers_config import FyersConfig, setup_logging
from fyers_connect import FyersConnect
from pivot_points import calculate_pivot_points, _calculate_pivot_standard
from constant import TOP_N_CLOSEST, TIMESTAMP_FORMAT, COMMON_COLS
from config_loader import get_config

# Configure logging
logger = logging.getLogger(__name__)

def create_reports_directory():
    """Create reports directory if it doesn't exist."""
    config = get_config()
    reports_dir = config.output_dir
    if not os.path.exists(reports_dir):
        os.makedirs(reports_dir)
        logger.info(f"Created reports directory: {reports_dir}")
    return reports_dir

def generate_timestamped_filename(base_name, symbol, extension="csv"):
    """Generate a timestamped filename including the symbol."""
    timestamp = datetime.now().strftime(TIMESTAMP_FORMAT)
    return f"{base_name}_{symbol}_{timestamp}.{extension}"

def get_filtered_options(option_chain, fyers):
    """Filter options by delta range and prepare for pivot calculation."""
    config = get_config()
    calls_df = pd.DataFrame(option_chain)
    puts_df = calls_df.copy()

    # Filter by delta range
    calls_df = calls_df[calls_df['call_delta'].between(config.min_delta, config.max_delta)].copy()
    puts_df = puts_df[puts_df['put_delta'].abs().between(config.min_delta, config.max_delta)].copy()

    # Assign type
    calls_df['type'] = 'CE'
    puts_df['type'] = 'PE'

    # Map relevant columns to a common name, including LTP
    # For calls
    calls_df['symbol'] = calls_df['call_symbol']
    calls_df['LTP'] = calls_df['call_price']
    calls_df['volume'] = calls_df['call_volume']
    calls_df['prev_close'] = calls_df['call_prev_close'] if 'call_prev_close' in calls_df.columns else None
    calls_df['change'] = calls_df['call_change']
    calls_df['change_percent'] = calls_df['call_change_percent'] if 'call_change_percent' in calls_df.columns else None
    calls_df['delta'] = calls_df['call_delta']

    # Fetch weekly OHLC for each call option
    calls_ohlc_data = []
    for _, row in calls_df.iterrows():
        ohlc_dict = fyers._fetch_weekly_ohlc_for_option(row['call_symbol'])
        calls_ohlc_data.append(pd.DataFrame([ohlc_dict]))
    calls_ohlc_df = pd.concat(calls_ohlc_data, ignore_index=True)
    calls_df = pd.concat([calls_df.reset_index(drop=True), calls_ohlc_df], axis=1)

    # For puts
    puts_df['symbol'] = puts_df['put_symbol']
    puts_df['LTP'] = puts_df['put_price']
    puts_df['volume'] = puts_df['put_volume']
    puts_df['prev_close'] = puts_df['put_prev_close'] if 'put_prev_close' in puts_df.columns else None
    puts_df['change'] = puts_df['put_change']
    puts_df['change_percent'] = puts_df['put_change_percent'] if 'put_change_percent' in puts_df.columns else None
    puts_df['delta'] = puts_df['put_delta']

    # Fetch weekly OHLC for each put option
    puts_ohlc_data = []
    for _, row in puts_df.iterrows():
        ohlc_dict = fyers._fetch_weekly_ohlc_for_option(row['put_symbol'])
        puts_ohlc_data.append(pd.DataFrame([ohlc_dict]))
    puts_ohlc_df = pd.concat(puts_ohlc_data, ignore_index=True)
    puts_df = pd.concat([puts_df.reset_index(drop=True), puts_ohlc_df], axis=1)

    # Select and concatenate relevant columns
    common_cols = COMMON_COLS
    # Ensure all common_cols exist in both dataframes before concatenation
    for col in common_cols:
        if col not in calls_df.columns:
            calls_df[col] = None
        if col not in puts_df.columns:
            puts_df[col] = None

    return pd.concat([calls_df[common_cols], puts_df[common_cols]], ignore_index=True)

def calculate_pivot_points(df):
    """Calculate pivot points for each option based on its own OHLC data."""
    
    # Ensure required columns are numeric, coercing errors
    for col in ['high', 'low', 'close']:
        df[col] = pd.to_numeric(df[col], errors='coerce')

    # Drop rows where essential data is missing
    df.dropna(subset=['high', 'low', 'close'], inplace=True)

    if df.empty:
        logger.warning("No options with valid high, low, close data found. Skipping pivot calculation.")
        return pd.DataFrame()

    def calculate_row_pivots(row):
        """Helper function to calculate pivots for a single row."""
        return _calculate_pivot_standard(row['high'], row['low'], row['close'])

    df['pivots'] = df.apply(calculate_row_pivots, axis=1)
    
    # Log a sample of the calculated pivots
    if not df.empty:
        sample_symbol = df.iloc[0]['symbol']
        sample_pivots = df.iloc[0]['pivots']
        logger.info(f"Calculated pivots for {sample_symbol}: {sample_pivots}")

    return df

def find_closest_to_positive_pivots(df):
    """
    Find options with LTP closest to any of their positive pivot levels.
    Returns a filtered DataFrame with options that have LTP closest to positive pivot levels.
    """
    def get_closest_positive_pivot_info(row):
        """Get information about the closest positive pivot level for a given row."""
        pivots = row['pivots']
        ltp = row['LTP']
        
        if not isinstance(pivots, dict) or pd.isna(ltp):
            return None, None, float('inf')
        
        positive_pivots = {k: v for k, v in pivots.items() if v > 0}
        
        if not positive_pivots:
            return None, None, float('inf')
        
        closest_pivot = min(positive_pivots.items(), key=lambda x: abs(ltp - x[1]))
        pivot_level, pivot_value = closest_pivot
        distance = abs(ltp - pivot_value)
        
        return pivot_level, pivot_value, distance
    
    df_copy = df.copy()
    pivot_info = df_copy.apply(get_closest_positive_pivot_info, axis=1, result_type='expand')
    df_copy['closest_positive_pivot_level'] = pivot_info[0]
    df_copy['closest_positive_pivot_value'] = pivot_info[1]
    df_copy['distance_to_positive_pivot'] = pivot_info[2]
    
    filtered_df = df_copy[df_copy['closest_positive_pivot_level'].notna()].copy()
    
    if filtered_df.empty:
        return pd.DataFrame()

    filtered_df['distance_to_positive_pivot_pct'] = (
        filtered_df['distance_to_positive_pivot'] / filtered_df['closest_positive_pivot_value'] * 100
    )
    
    return filtered_df

def find_best_match_to_min_positive_pivot(df):
    """
    1. For each option, find the minimum positive pivot level.
    2. Calculate the distance between LTP and this minimum positive pivot.
    3. Return the single option with the smallest distance overall.
    """
    df_copy = df.copy()

    def get_min_positive_pivot_info(row):
        pivots = row['pivots']
        ltp = row['LTP']

        if not isinstance(pivots, dict) or pd.isna(ltp):
            return None, float('inf')

        positive_pivots = [v for v in pivots.values() if v > 0]

        if not positive_pivots:
            return None, float('inf')

        min_positive_pivot = min(positive_pivots)
        distance = abs(ltp - min_positive_pivot)

        return min_positive_pivot, distance

    pivot_info = df_copy.apply(get_min_positive_pivot_info, axis=1, result_type='expand')
    df_copy['min_positive_pivot'] = pivot_info[0]
    df_copy['distance_to_min_positive_pivot'] = pivot_info[1]

    df_copy.dropna(subset=['min_positive_pivot'], inplace=True)

    if df_copy.empty:
        return pd.DataFrame()

    best_match_index = df_copy['distance_to_min_positive_pivot'].idxmin()
    best_match_df = df_copy.loc[[best_match_index]]

    return best_match_df

def find_options_closest_to_min_positive_pivot(df):
    """
    Find options where LTP is closest to the positive minimum of all pivot levels 
    [P,R1,R2,R3,R4,R5,S1,S2,S3,S4,S5].
    Returns 1 or more options that are closest to their minimum positive pivot, 
    or zero if no close matches.
    """
    def get_min_positive_pivot_info(row):
        """Get information about the minimum positive pivot level for a given row."""
        pivots = row['pivots']
        ltp = row['LTP']
        
        if not isinstance(pivots, dict) or pd.isna(ltp):
            return None, None, float('inf')
        
        # Define the pivot levels we're interested in
        pivot_levels = ['Pivot', 'R1', 'R2', 'R3', 'R4', 'R5', 'S1', 'S2', 'S3', 'S4', 'S5']
        positive_pivot_values = []
        
        for level in pivot_levels:
            if level in pivots and pivots[level] > 0:
                positive_pivot_values.append(pivots[level])
        
        if not positive_pivot_values:
            return None, None, float('inf')
        
        # Find the minimum positive pivot value
        min_positive_pivot = min(positive_pivot_values)
        distance = abs(ltp - min_positive_pivot)
        
        # Find which level corresponds to this minimum value
        min_pivot_level = None
        for level in pivot_levels:
            if level in pivots and pivots[level] == min_positive_pivot:
                min_pivot_level = level
                break
        
        return min_pivot_level, min_positive_pivot, distance
    
    df_copy = df.copy()
    pivot_info = df_copy.apply(get_min_positive_pivot_info, axis=1, result_type='expand')
    df_copy['min_positive_pivot_level'] = pivot_info[0]
    df_copy['min_positive_pivot_value'] = pivot_info[1]
    df_copy['distance_to_min_positive_pivot'] = pivot_info[2]
    
    # Filter out options without valid positive pivots
    filtered_df = df_copy[df_copy['min_positive_pivot_level'].notna()].copy()
    
    if filtered_df.empty:
        return pd.DataFrame()
    
    # Sort by distance to min positive pivot
    closest_matches = filtered_df.sort_values('distance_to_min_positive_pivot')
    
    # Calculate percentage distance for better understanding
    closest_matches['distance_to_min_positive_pivot_pct'] = (
        closest_matches['distance_to_min_positive_pivot'] / closest_matches['min_positive_pivot_value'] * 100
    )
    
    return closest_matches

def main():
    """Main entry point of the application."""
    setup_logging()
    
    # Load and validate configuration
    config = get_config()
    if not config.validate_config():
        logger.error("Configuration validation failed. Exiting.")
        return
    
    reports_dir = create_reports_directory()

    # Step 1: Connect to Fyers API with config path
    fyers_config = FyersConfig(env_path=config.env_path)
    fyers = FyersConnect(fyers_config)
    if not fyers.login():
        logger.error("Fyers login failed. Exiting.")
        return

    symbols_to_process = config.symbols
    logger.info(f"Configured symbols to process: {symbols_to_process}")
    for symbol in symbols_to_process:
        logger.info(f"--- Starting processing for symbol: {symbol} ---")
        logger.info(f"Processing symbol: {symbol} with configuration: {config.get_symbol_config(symbol)}")
        symbol_config_data = config.get_symbol_config(symbol)
        num_strikes = symbol_config_data.get("num_strikes_each_side", 10) # Default to 10 if not specified
        try:
            option_chain = fyers.get_option_chain(symbol, expiry_type='MONTHLY', num_strikes_each_side=num_strikes)
        except ValueError as e:
            logger.error(f"Error: {e}")
            continue

        if not option_chain:
            logger.error(f"No option chain data fetched for {symbol}. Skipping report generation.")
            continue

        # Step 3: Filter options
        try:
            df = get_filtered_options(option_chain, fyers)
        except Exception as e:
            logger.error(f"Error filtering options for {symbol}: {e}. Skipping report generation.")
            continue

        if df.empty:
            logger.info(f"No options found in the specified delta range for {symbol}. Skipping report generation.")
            continue

        # Step 4: Calculate pivot points
        try:
            df = calculate_pivot_points(df)
        except Exception as e:
            logger.error(f"Error calculating pivot points for {symbol}: {e}. Skipping report generation.")
            continue

        df['LTP'] = pd.to_numeric(df['LTP'], errors='coerce')
        df.dropna(subset=['LTP'], inplace=True)
        if df.empty:
            logger.info(f"No pivot points calculated for any option or no valid LTP data for {symbol}. Skipping report generation.")
            continue

        # --- Report 1: All filtered options with pivots ---
        # Ensure 'pivots' column contains dictionaries and not NaNs
        df_with_pivots = df[df['pivots'].apply(lambda x: isinstance(x, dict) and bool(x))].copy()

        if df_with_pivots.empty:
            logger.info(f"No options with valid pivot data to report for {symbol}. Skipping report generation.")
            continue

        pivot_df = pd.DataFrame.from_records(df_with_pivots['pivots'].tolist(), index=df_with_pivots.index)
        all_options_df = pd.concat([df_with_pivots.drop(columns=['pivots']), pivot_df], axis=1)
        all_filename = generate_timestamped_filename("filtered_pivot", symbol)
        all_filepath = os.path.join(reports_dir, all_filename)
        logger.debug(f"Attempting to export filtered_pivot for {symbol} to {all_filepath}")
        all_options_df.to_csv(all_filepath, index=False)
        logger.info(f"Exported {len(all_options_df)} filtered options with pivots to {all_filepath}")

        # --- Report 2 & 3: Unified Top 5 and Final Shortlisted Options (by min positive pivot) ---
        min_pivot_df = find_options_closest_to_min_positive_pivot(df.copy())
        if not min_pivot_df.empty:
            # Sort by distance to min positive pivot and take top N
            topN_min_pivot_df = min_pivot_df.sort_values('distance_to_min_positive_pivot').head(TOP_N_CLOSEST)
            pivots_expanded_df = pd.DataFrame.from_records(topN_min_pivot_df['pivots'].tolist(), index=topN_min_pivot_df.index)
            report2_df = pd.concat([topN_min_pivot_df.drop(columns=['pivots']), pivots_expanded_df], axis=1)
            positive_pivot_filename = generate_timestamped_filename("closest_positive_pivots", symbol)
            positive_pivot_filepath = os.path.join(reports_dir, positive_pivot_filename)
            logger.debug(f"Attempting to export closest_positive_pivots for {symbol} to {positive_pivot_filepath}")
            report2_df.to_csv(positive_pivot_filepath, index=False)
            logger.info(f"Exported top {len(report2_df)} options closest to their minimum positive pivots to {positive_pivot_filepath}")

            # Final shortlisted: those with the absolute minimum distance among the top N
            min_distance = topN_min_pivot_df['distance_to_min_positive_pivot'].min()
            final_shortlisted_options = topN_min_pivot_df[topN_min_pivot_df['distance_to_min_positive_pivot'] == min_distance].copy()
            pivots_expanded_final = pd.DataFrame.from_records(final_shortlisted_options['pivots'].tolist(), index=final_shortlisted_options.index)
            final_report_df = pd.concat([final_shortlisted_options, pivots_expanded_final], axis=1)
            shortlisted_filename = generate_timestamped_filename("final_shortlisted", symbol)
            shortlisted_filepath = os.path.join(reports_dir, shortlisted_filename)
            logger.debug(f"Attempting to export final_shortlisted for {symbol} to {shortlisted_filepath}")
            final_report_df.to_csv(shortlisted_filepath, index=False)
            logger.info(f"Exported {len(final_report_df)} final shortlisted option(s) to {shortlisted_filepath}")

            # Display the final shortlisted options
            logger.info(f"Final shortlisted option(s) (LTP closest to minimum positive pivot levels) for {symbol}: {len(final_report_df)}")
            # Define user-friendly column names
            display_cols_final = {
                'symbol': 'Symbol',
                'type': 'Type',
                'strike': 'Strike',
                'LTP': 'Last Traded Price',
                'min_positive_pivot_level': 'Min Positive Pivot Level',
                'min_positive_pivot_value': 'Min Positive Pivot Value',
                'distance_to_min_positive_pivot': 'Distance to Min Positive Pivot',
                'distance_to_min_positive_pivot_pct': 'Distance %'
            }

            # Rename columns for display
            display_df = final_report_df.rename(columns=display_cols_final)

            # Format float columns for better readability
            float_format_cols = [
                'Last Traded Price',
                'Min Positive Pivot Value',
                'Distance to Min Positive Pivot',
                'Distance %'
            ]
            for col in float_format_cols:
                if col in display_df.columns:
                    display_df[col] = display_df[col].map(lambda x: f"{x:.2f}")

            logger.info(f"Final Shortlisted Option(s) - {len(final_report_df)} match(es) (Closest to Minimum Positive Pivot) for {symbol}:")
            logger.info("=" * 150)
            logger.info(display_df[list(display_cols_final.values())].to_string(index=False))
            logger.info("=" * 150)
        else:
            logger.info(f"No options found closest to minimum positive pivot levels for {symbol}.")
            logger.info(f"\nFinal Shortlisted Options for {symbol}: 0 matches found")
        logger.info(f"--- Finished processing for symbol: {symbol} ---")

    # --- Summary ---
    logger.info(f"\nSummary:")
    logger.info(f"Total options analyzed: {len(df)}")
    if 'report2_df' in locals() and not report2_df.empty:
        logger.info(f"Top {TOP_N_CLOSEST} options closest to minimum positive pivots: {len(report2_df)}")
    logger.info(f"Final shortlisted options (closest to min positive pivot): {len(final_report_df) if 'final_report_df' in locals() and not final_report_df.empty else 0}")
    logger.info(f"Reports saved in: {reports_dir}")

if __name__ == "__main__":
    main()
