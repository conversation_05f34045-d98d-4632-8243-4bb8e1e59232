import os
import requests
import yaml
import logging
from datetime import datetime
from urllib.parse import urlparse
from typing import List, Dict, Tuple

logger = logging.getLogger(__name__)

def get_config():
    """Loads the configuration from config.yaml."""
    with open(os.path.join(os.path.dirname(os.path.abspath(__file__)), "config.yaml"), 'r') as f:
        return yaml.safe_load(f)

def download_file(url: str, destination: str) -> bool:
    """
    Downloads a file from a URL to a destination.

    Args:
        url: URL to download from
        destination: Local file path to save to

    Returns:
        True if successful, False otherwise
    """
    try:
        logger.info(f"Downloading from {url}...")
        response = requests.get(url, timeout=30)
        response.raise_for_status()

        with open(destination, 'wb') as f:
            f.write(response.content)

        logger.info(f"Successfully downloaded to {destination}")
        return True

    except requests.exceptions.RequestException as e:
        logger.error(f"Failed to download {url}: {e}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error downloading {url}: {e}")
        return False

def determine_csv_filename(url: str) -> str:
    """
    Determine the local CSV filename based on the URL.

    Args:
        url: URL to analyze

    Returns:
        Local CSV filename
    """
    # Extract filename from URL
    parsed_url = urlparse(url)
    filename = os.path.basename(parsed_url.path)

    # Map known patterns to standard filenames
    if 'NSE_CM' in filename or 'NSE_CM' in url:
        return 'NSE_CM.csv'
    elif 'NSE_FO' in filename or 'NSE_FO' in url:
        return 'NSE_FO.csv'
    elif 'MCX' in filename or 'MCX' in url:
        return 'MCX.csv'
    elif filename.endswith('.csv'):
        return filename
    else:
        # Default fallback - use the last part of the URL path
        return f"{filename}.csv" if not filename.endswith('.csv') else filename

def backup_existing_file(local_file_path: str, mastersymbol_dir: str) -> bool:
    """
    Backup an existing file to the mastersymbol directory.

    Args:
        local_file_path: Path to the file to backup
        mastersymbol_dir: Directory to store backups

    Returns:
        True if backup was successful or file didn't exist, False otherwise
    """
    try:
        if os.path.exists(local_file_path):
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_prefix = os.path.splitext(os.path.basename(local_file_path))[0]
            backup_path = os.path.join(mastersymbol_dir, f"{file_prefix}_{timestamp}.csv")

            os.rename(local_file_path, backup_path)
            logger.info(f"Backed up existing file to {backup_path}")
            print(f"Backed up existing file to {backup_path}")

        return True

    except Exception as e:
        logger.error(f"Failed to backup {local_file_path}: {e}")
        return False

def download_symbols_from_urls(urls: List[str]) -> Dict[str, bool]:
    """
    Download symbol files from multiple URLs.

    Args:
        urls: List of URLs to download from

    Returns:
        Dictionary mapping local filename to success status
    """
    results = {}
    mastersymbol_dir = 'mastersymbol'

    # Create or clear the mastersymbol directory
    try:
        if not os.path.exists(mastersymbol_dir):
            os.makedirs(mastersymbol_dir)
            logger.info(f"Created backup directory: {mastersymbol_dir}")
        else:
            # Clean up old backups (keep only recent ones)
            cleanup_old_backups(mastersymbol_dir)
    except Exception as e:
        logger.error(f"Failed to setup backup directory: {e}")
        return results

    # Process each URL
    for url in urls:
        try:
            # Determine local filename
            local_file_path = determine_csv_filename(url)

            logger.info(f"Processing URL: {url} -> {local_file_path}")

            # Backup existing file
            if not backup_existing_file(local_file_path, mastersymbol_dir):
                logger.warning(f"Backup failed for {local_file_path}, continuing anyway")

            # Download the new file
            success = download_file(url, local_file_path)
            results[local_file_path] = success

            if success:
                print(f"✓ Downloaded and saved: {local_file_path}")
            else:
                print(f"✗ Failed to download: {local_file_path}")

        except Exception as e:
            logger.error(f"Error processing URL {url}: {e}")
            results[url] = False

    return results

def cleanup_old_backups(mastersymbol_dir: str, keep_count: int = 5):
    """
    Clean up old backup files, keeping only the most recent ones.

    Args:
        mastersymbol_dir: Directory containing backups
        keep_count: Number of recent backups to keep per file type
    """
    try:
        if not os.path.exists(mastersymbol_dir):
            return

        # Group files by prefix
        file_groups = {}
        for filename in os.listdir(mastersymbol_dir):
            if filename.endswith('.csv'):
                # Extract prefix (everything before the timestamp)
                parts = filename.split('_')
                if len(parts) >= 2:
                    prefix = '_'.join(parts[:-2])  # Everything except timestamp parts
                    if prefix not in file_groups:
                        file_groups[prefix] = []
                    file_groups[prefix].append(filename)

        # Clean up each group
        for prefix, files in file_groups.items():
            if len(files) > keep_count:
                # Sort by modification time (newest first)
                files_with_time = [(f, os.path.getmtime(os.path.join(mastersymbol_dir, f))) for f in files]
                files_with_time.sort(key=lambda x: x[1], reverse=True)

                # Remove old files
                for filename, _ in files_with_time[keep_count:]:
                    file_path = os.path.join(mastersymbol_dir, filename)
                    os.remove(file_path)
                    logger.debug(f"Removed old backup: {filename}")

    except Exception as e:
        logger.error(f"Error cleaning up old backups: {e}")

def main():
    """Main function to download and backup the symbol files."""
    try:
        # Setup logging
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )

        logger.info("Starting symbol file download process")

        config = get_config()
        remote_urls = config['general']['fyers_api_url']

        # Ensure remote_urls is a list
        if isinstance(remote_urls, str):
            remote_urls = [remote_urls]

        if not remote_urls:
            logger.error("No URLs configured in fyers_api_url")
            print("Error: No URLs configured in config.yaml")
            return False

        logger.info(f"Found {len(remote_urls)} URLs to download")
        print(f"Downloading symbol files from {len(remote_urls)} sources...")

        # Download all files
        results = download_symbols_from_urls(remote_urls)

        # Report results
        successful_downloads = sum(1 for success in results.values() if success)
        total_downloads = len(results)

        logger.info(f"Download complete: {successful_downloads}/{total_downloads} successful")
        print(f"\nDownload Summary:")
        print(f"  Successful: {successful_downloads}/{total_downloads}")

        for filename, success in results.items():
            status = "✓" if success else "✗"
            print(f"  {status} {filename}")

        return successful_downloads == total_downloads

    except Exception as e:
        logger.error(f"Symbol download failed: {e}")
        print(f"Error: {e}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)