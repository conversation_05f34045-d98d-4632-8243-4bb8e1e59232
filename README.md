# Multi-Market Scanner

A comprehensive Python application that scans multiple market types (EQUITY, INDEX, FUTURES, OPTIONS) from NSE, filters them based on configurable criteria, and generates detailed reports.

## Features

- **Multi-Market Support**: Unified scanner for EQUITY, INDEX, FUTURES, and OPTIONS symbols from NSE_CM.csv and NSE_FO.csv
- **Universal Symbol Parsing**: Handles all market types with a unified parsing engine
- **Intelligent Options Filtering**: Dynamic strike price filtering based on real-time spot prices from EQUITY/INDEX markets
- **Smart Strike Range Calculation**: Automatic strike interval detection (50 for NIFTY, 100 for BANKNIFTY, etc.)
- **Market Data**: Fetches real-time market data using Fyers API v3 with rate limiting and retry logic
- **Advanced Filtering**: Volume, LTP, options chain filtering with INDEX market volume exemption, and technical indicators (MAE)
- **Options Chain Analysis**: Intelligent strike selection around ATM with CE/PE pairing and same expiry requirements
- **Market-Specific Reports**: Creates separate CSV reports with appropriate columns for each market type
- **Performance Optimized**: Batch processing, parallel data fetching, and efficient memory usage
- **Daily Authentication**: Handles Fyers API authentication with daily token caching

## Requirements

- Python 3.7+
- Required packages: `pyyaml`, `fyers-apiv3`, `pandas`, `ta`
- NSE_CM.csv and NSE_FO.csv files (automatically downloaded)
- Fyers API credentials in .env file

## Installation

1. Install required packages:
```bash
pip install pyyaml fyers-apiv3 pandas ta
```

2. Ensure you have the following files in the project directory:
   - `config.yaml` - Configuration file
   - `.env` - Fyers API credentials (path specified in config.yaml)

   Note: NSE_CM.csv and NSE_FO.csv files are automatically downloaded from NSE

## Configuration

Edit `config.yaml` to customize the scanning parameters:

```yaml
general:
  env_path: "C:/Users/<USER>/Desktop/Python/.env"
  output_dir: "reports"

symbols:
  - "NIFTY"
  - "BANKNIFTY"

options_filter:
  min_volume: 5
  min_ltp_price: 2500
  max_ltp_price: 5000
```

### Configuration Parameters

- **env_path**: Path to your .env file containing Fyers API credentials
- **output_dir**: Directory where reports will be saved
- **symbols**: List of underlying symbols to scan (NIFTY, BANKNIFTY)
- **min_volume**: Minimum trading volume filter
- **min_ltp_price**: Minimum LTP price filter
- **max_ltp_price**: Maximum LTP price filter

## Usage

### Run the Scanner

```bash
python main.py
```

### Test the Implementation

```bash
python test_scanner.py
```

## Output

The scanner generates two types of reports in the configured output directory:

### 1. CSV Report (`index_scan_YYYYMMDD_HHMMSS.csv`)

Contains filtered symbols with the following columns:
- `strike` - Strike price
- `expiry_date` - Expiry date
- `type` - Option type (CE/PE)
- `symbol` - Full NSE symbol
- `LTP` - Last traded price
- `volume` - Trading volume
- `open` - Opening price
- `high` - Day's high price
- `low` - Day's low price
- `close` - Closing price
- `prev_close` - Previous day's close
- `change` - Price change
- `change_percent` - Percentage change

### 2. Summary Report (`index_scan_summary_YYYYMMDD_HHMMSS.txt`)

Contains:
- Summary statistics (total symbols, breakdown by underlying and option type)
- Top 10 symbols by volume
- Top 10 symbols by LTP

## Project Structure

```
multitimeframe_scanner/
├── main.py                      # Main entry point
├── config_loader.py             # Configuration management
├── unified_scanner.py           # Unified scanner orchestrator
├── market_type_scanner.py       # Market type specific scanners
├── universal_symbol_parser.py   # Universal symbol parsing engine
├── options_chain_filter.py      # Intelligent options filtering
├── fyers_client.py              # Fyers API client
├── fyers_config.py              # Fyers authentication
├── report_generator.py          # Report generation
├── technical_indicators.py      # Technical analysis indicators
├── symbol_downloader.py         # Symbol file downloader
├── test_multi_market_scanner.py # Test suite
├── config.yaml                  # Configuration file
├── NSE_CM.csv                   # NSE Cash Market data
├── NSE_FO.csv                   # NSE F&O instruments data
└── reports/                     # Generated reports directory
```

## How It Works

1. **Configuration Loading**: Loads settings from `config.yaml` for all market types
2. **Symbol Loading**: Unified parser loads symbols from NSE_CM.csv (equity/index) and NSE_FO.csv (futures/options)
3. **Spot Price Fetching**: For options filtering, fetches real-time spot prices from EQUITY/INDEX markets
4. **Intelligent Options Filtering**: Dynamically calculates strike ranges based on spot prices and strike intervals
5. **Authentication**: Authenticates with Fyers API (daily token caching)
6. **Market Data**: Fetches real-time quotes for filtered symbols with rate limiting
7. **Multi-Stage Filtering**: Applies volume (except INDEX), LTP, and market-specific filters
8. **Options Chain Analysis**: Creates intelligent chains around ATM strikes with CE/PE pairing
9. **Technical Analysis**: Optionally calculates MAE indicators with smoothing options
10. **Market-Specific Reports**: Generates separate CSV and summary reports with appropriate columns for each market type

## Filtering Logic

Symbols are included in the final report if they meet ALL of the following criteria:
- Underlying symbol is in the configured symbols list (NIFTY, BANKNIFTY)
- Expiry date is in current month, next month, or next-next month
- Trading volume >= configured minimum volume
- LTP is within the configured price range

## Error Handling

- Comprehensive logging with file rotation
- Graceful handling of API failures
- Configuration validation
- Missing file detection

## Notes

- The scanner processes options for 3 months: current month + next 2 months
- Fyers API authentication is required only once per day
- Large symbol lists are processed in batches to respect API limits
- All prices are rounded to 2 decimal places in reports

## Troubleshooting

1. **Authentication Issues**: Ensure your .env file contains valid Fyers API credentials
2. **No Symbols Found**: Check if NSE_FO.csv is present and contains data for target months
3. **Filter Too Restrictive**: Adjust volume and LTP filters in config.yaml
4. **API Limits**: The scanner processes symbols in batches to avoid rate limits

For detailed logs, check the log files generated in the application directory.
