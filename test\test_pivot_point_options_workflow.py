"""
Test file for testing pivot point indicator integration for option trading workflow.
Tests the complete workflow with specific option symbols and generates detailed console reports.
"""

import os
import sys
import logging
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

# Add current directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config_loader import ConfigLoader
from fyers_client import FyersClient
from fyers_config import FyersConfig
from pivot_point_integration import PivotPointIntegration, PivotPointData
from pivot_points import _calculate_pivot_standard
from market_type_scanner import FilteredSymbol, MarketData
from universal_symbol_parser import UniversalSymbolParser
from report_generator import ReportGenerator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class SymbolTestData:
    """Data class for test symbol information."""
    symbol: str
    underlying: str
    option_type: str
    strike_price: float
    expiry_date: str
    market_data: Optional[MarketData] = None
    pivot_data: Optional[PivotPointData] = None

class PivotPointOptionsWorkflowTest:
    """Test class for pivot point indicator integration with option trading workflow."""
    
    def __init__(self):
        """Initialize the test class."""
        self.test_symbols = [
            'NSE:NHPC25JUL96PE',
            'NSE:HFCL25JUL87.5PE'
        ]
        self.config = None
        self.fyers_client = None
        self.pivot_integration = None
        self.test_results = {}
        self.symbol_data = []
        
    def setup_test_environment(self):
        """Setup test environment and initialize components."""
        logger.info("=" * 80)
        logger.info("SETTING UP PIVOT POINT OPTIONS WORKFLOW TEST ENVIRONMENT")
        logger.info("=" * 80)
        
        try:
            # Change to script directory
            os.chdir(os.path.dirname(os.path.abspath(__file__)))
            
            # Load configuration
            self.config = ConfigLoader('config.yaml')
            logger.info("✓ Configuration loaded successfully")
            
            # Initialize Fyers client
            self.fyers_client = FyersClient(env_path=self.config.env_path)
            
            # Login to Fyers
            if self.fyers_client.authenticate():
                logger.info("✓ Fyers client authenticated successfully")
            else:
                logger.warning("⚠️ Fyers client authentication failed - using mock data")
            
            # Initialize pivot point integration
            self.pivot_integration = PivotPointIntegration(self.config, self.fyers_client)
            logger.info("✓ Pivot point integration initialized")
            
            logger.info(f"✓ Test environment setup completed")
            logger.info(f"  - Pivot Point Enabled: {self.pivot_integration.is_enabled()}")
            logger.info(f"  - Calculation Type: {self.pivot_integration.calculation_type}")
            logger.info(f"  - Top N Closest: {self.pivot_integration.top_n_closest}")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ Failed to setup test environment: {e}")
            return False
    
    def parse_test_symbols(self):
        """Parse test symbols and extract symbol information."""
        logger.info("=" * 80)
        logger.info("PARSING TEST SYMBOLS")
        logger.info("=" * 80)
        
        try:
            # Initialize symbol parser
            parser = UniversalSymbolParser(self.config, self.config.symbols)
            csv_file = self.config.get_csv_file_for_market_type('OPTIONS')
            
            for symbol in self.test_symbols:
                logger.info(f"Parsing symbol: {symbol}")
                
                try:
                    # Parse symbol
                    parsed_symbol = parser.parse_symbol(symbol, csv_file)
                    
                    if parsed_symbol:
                        # Extract symbol information
                        expiry_date = f"{parsed_symbol.expiry_year}{parsed_symbol.expiry_month}" if parsed_symbol.expiry_year and parsed_symbol.expiry_month else "Unknown"
                        symbol_data = SymbolTestData(
                            symbol=symbol,
                            underlying=parsed_symbol.underlying,
                            option_type=parsed_symbol.option_type,
                            strike_price=parsed_symbol.strike_price,
                            expiry_date=expiry_date
                        )
                        
                        self.symbol_data.append(symbol_data)
                        
                        logger.info(f"✓ Successfully parsed {symbol}")
                        logger.info(f"  - Underlying: {symbol_data.underlying}")
                        logger.info(f"  - Option Type: {symbol_data.option_type}")
                        logger.info(f"  - Strike Price: {symbol_data.strike_price}")
                        logger.info(f"  - Expiry Date: {symbol_data.expiry_date}")
                        
                        self.test_results[f"parse_{symbol}"] = True
                        
                    else:
                        logger.error(f"✗ Failed to parse symbol: {symbol}")
                        self.test_results[f"parse_{symbol}"] = False
                        
                except Exception as e:
                    logger.error(f"✗ Error parsing symbol {symbol}: {e}")
                    self.test_results[f"parse_{symbol}"] = False
            
            logger.info(f"✓ Symbol parsing completed. Parsed {len(self.symbol_data)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"✗ Symbol parsing failed: {e}")
            return False
    
    def fetch_market_data(self):
        """Fetch market data for test symbols."""
        logger.info("=" * 80)
        logger.info("FETCHING MARKET DATA")
        logger.info("=" * 80)
        
        try:
            for symbol_data in self.symbol_data:
                logger.info(f"Fetching market data for: {symbol_data.symbol}")
                
                try:
                    # Fetch market data using Fyers client
                    if self.fyers_client and self.fyers_client.is_authenticated():
                        market_data_dict = self.fyers_client.get_market_data([symbol_data.symbol])
                        if market_data_dict and symbol_data.symbol in market_data_dict:
                            data = market_data_dict[symbol_data.symbol]
                            # If data is a dict, use keys; if MarketData, use attributes
                            if isinstance(data, dict):
                                market_data = MarketData(
                                    symbol=symbol_data.symbol,
                                    ltp=data.get('ltp', 0.0),
                                    volume=data.get('volume', 0),
                                    open_price=data.get('open_price', 0.0),
                                    high_price=data.get('high_price', 0.0),
                                    low_price=data.get('low_price', 0.0),
                                    close_price=data.get('close_price', 0.0),
                                    prev_close=data.get('prev_close', 0.0),
                                    change=data.get('change', 0.0),
                                    change_percent=data.get('change_percent', 0.0)
                                )
                            else:
                                # Assume data is already a MarketData object
                                market_data = data
                            symbol_data.market_data = market_data
                            logger.info(f"✓ Market data fetched for {symbol_data.symbol}")
                            logger.info(f"  - LTP: ₹{market_data.ltp:.2f}")
                            logger.info(f"  - Volume: {market_data.volume:,}")
                            logger.info(f"  - Change: {market_data.change:.2f} ({market_data.change_percent:.2f}%)")
                            self.test_results[f"market_data_{symbol_data.symbol}"] = True
                        else:
                            logger.warning(f"⚠️ No market data received for {symbol_data.symbol}")
                            # Create mock market data for testing
                            symbol_data.market_data = self._create_mock_market_data(symbol_data)
                            self.test_results[f"market_data_{symbol_data.symbol}"] = False
                    else:
                        logger.warning(f"⚠️ Fyers client not authenticated, using mock data for {symbol_data.symbol}")
                        # Create mock market data for testing
                        symbol_data.market_data = self._create_mock_market_data(symbol_data)
                        self.test_results[f"market_data_{symbol_data.symbol}"] = False
                        
                except Exception as e:
                    logger.error(f"✗ Error fetching market data for {symbol_data.symbol}: {e}")
                    # Create mock market data for testing
                    symbol_data.market_data = self._create_mock_market_data(symbol_data)
                    self.test_results[f"market_data_{symbol_data.symbol}"] = False
            
            logger.info(f"✓ Market data fetching completed for {len(self.symbol_data)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"✗ Market data fetching failed: {e}")
            return False
    
    def _create_mock_market_data(self, symbol_data: SymbolTestData) -> MarketData:
        """Create mock market data for testing purposes."""
        # Generate realistic mock data based on option type and strike
        base_price = max(1.0, symbol_data.strike_price * 0.02)  # 2% of strike as base
        if symbol_data.option_type == 'PE':
            # Put options typically have lower premiums
            ltp = base_price * 0.8
        else:
            # Call options
            ltp = base_price
        return MarketData(
            symbol=symbol_data.symbol,
            ltp=round(ltp, 2),
            volume=50000,
            open_price=round(ltp * 1.02, 2),
            high_price=round(ltp * 1.05, 2),
            low_price=round(ltp * 0.95, 2),
            close_price=round(ltp * 0.98, 2),
            prev_close=round(ltp * 1.01, 2),
            change=round(ltp * 0.02, 2),
            change_percent=2.0
        )
    
    def calculate_pivot_points(self):
        """Calculate pivot points for test symbols."""
        logger.info("=" * 80)
        logger.info("CALCULATING PIVOT POINTS")
        logger.info("=" * 80)
        
        try:
            for symbol_data in self.symbol_data:
                logger.info(f"Calculating pivot points for: {symbol_data.symbol}")
                
                try:
                    # Calculate pivot points using integration
                    pivot_data = self.pivot_integration.calculate_pivot_points_for_symbol(
                        symbol_data.symbol, 
                        symbol_data.market_data
                    )
                    
                    if pivot_data:
                        symbol_data.pivot_data = pivot_data
                        
                        logger.info(f"✓ Pivot points calculated for {symbol_data.symbol}")
                        logger.info(f"  - Pivot Point: {pivot_data.pivot_levels.get('Pivot', 'N/A')}")
                        logger.info(f"  - R1: {pivot_data.pivot_levels.get('R1', 'N/A')}")
                        logger.info(f"  - S1: {pivot_data.pivot_levels.get('S1', 'N/A')}")
                        logger.info(f"  - R2: {pivot_data.pivot_levels.get('R2', 'N/A')}")
                        logger.info(f"  - S2: {pivot_data.pivot_levels.get('S2', 'N/A')}")
                        
                        if pivot_data.closest_positive_pivot_level:
                            logger.info(f"  - Closest Positive Pivot: {pivot_data.closest_positive_pivot_level} = {pivot_data.closest_positive_pivot_value}")
                            logger.info(f"  - Distance: {pivot_data.distance_to_positive_pivot:.2f} ({pivot_data.distance_to_positive_pivot_pct:.2f}%)")
                        
                        if pivot_data.min_positive_pivot_level:
                            logger.info(f"  - Min Positive Pivot: {pivot_data.min_positive_pivot_level} = {pivot_data.min_positive_pivot_value}")
                            logger.info(f"  - Distance: {pivot_data.distance_to_min_positive_pivot:.2f} ({pivot_data.distance_to_min_positive_pivot_pct:.2f}%)")
                        
                        self.test_results[f"pivot_{symbol_data.symbol}"] = True
                        
                    else:
                        logger.warning(f"⚠️ No pivot data calculated for {symbol_data.symbol}")
                        # Create mock pivot data for testing
                        symbol_data.pivot_data = self._create_mock_pivot_data(symbol_data)
                        self.test_results[f"pivot_{symbol_data.symbol}"] = False
                        
                except Exception as e:
                    logger.error(f"✗ Error calculating pivot points for {symbol_data.symbol}: {e}")
                    # Create mock pivot data for testing
                    symbol_data.pivot_data = self._create_mock_pivot_data(symbol_data)
                    self.test_results[f"pivot_{symbol_data.symbol}"] = False
            
            logger.info(f"✓ Pivot point calculation completed for {len(self.symbol_data)} symbols")
            return True
            
        except Exception as e:
            logger.error(f"✗ Pivot point calculation failed: {e}")
            return False
    
    def _create_mock_pivot_data(self, symbol_data: SymbolTestData) -> PivotPointData:
        """Create mock pivot data for testing purposes."""
        # Generate realistic mock OHLC data
        ltp = symbol_data.market_data.ltp if symbol_data.market_data else 10.0
        high = ltp * 1.1
        low = ltp * 0.9
        close = ltp
        
        # Calculate pivot points using standard formula
        pivot_levels = _calculate_pivot_standard(high, low, close)
        
        # Create pivot data object
        pivot_data = PivotPointData(pivot_levels=pivot_levels)
        
        # Calculate closest positive pivot (mock calculation)
        current_price = ltp
        positive_pivots = {}
        
        for level, value in pivot_levels.items():
            if value > current_price:
                positive_pivots[level] = value
        
        if positive_pivots:
            # Find closest positive pivot
            closest_level = min(positive_pivots.keys(), key=lambda k: positive_pivots[k] - current_price)
            closest_value = positive_pivots[closest_level]
            
            pivot_data.closest_positive_pivot_level = closest_level
            pivot_data.closest_positive_pivot_value = closest_value
            pivot_data.distance_to_positive_pivot = closest_value - current_price
            pivot_data.distance_to_positive_pivot_pct = ((closest_value - current_price) / current_price) * 100
            
            # Find minimum positive pivot
            min_level = min(positive_pivots.keys(), key=lambda k: positive_pivots[k])
            min_value = positive_pivots[min_level]
            
            pivot_data.min_positive_pivot_level = min_level
            pivot_data.min_positive_pivot_value = min_value
            pivot_data.distance_to_min_positive_pivot = min_value - current_price
            pivot_data.distance_to_min_positive_pivot_pct = ((min_value - current_price) / current_price) * 100
        
        return pivot_data
    
    def generate_trading_signals(self):
        """Generate trading signals based on pivot point analysis."""
        logger.info("=" * 80)
        logger.info("GENERATING TRADING SIGNALS")
        logger.info("=" * 80)
        
        try:
            for symbol_data in self.symbol_data:
                logger.info(f"Analyzing trading signals for: {symbol_data.symbol}")
                
                if not symbol_data.market_data or not symbol_data.pivot_data:
                    logger.warning(f"⚠️ Insufficient data for signal generation: {symbol_data.symbol}")
                    continue
                
                current_price = symbol_data.market_data.ltp
                pivot_levels = symbol_data.pivot_data.pivot_levels
                
                # Analyze price position relative to pivot levels
                signals = []
                
                # Check support and resistance levels
                pivot_point = pivot_levels.get('Pivot', 0)
                r1 = pivot_levels.get('R1', 0)
                s1 = pivot_levels.get('S1', 0)
                r2 = pivot_levels.get('R2', 0)
                s2 = pivot_levels.get('S2', 0)
                
                if current_price > pivot_point:
                    if current_price < r1:
                        signals.append(f"Price above Pivot ({pivot_point:.2f}), approaching R1 ({r1:.2f})")
                        signals.append("SIGNAL: Potential resistance at R1 - Consider profit booking")
                    elif current_price > r1 and current_price < r2:
                        signals.append(f"Price above R1 ({r1:.2f}), approaching R2 ({r2:.2f})")
                        signals.append("SIGNAL: Strong bullish momentum - Watch for R2 resistance")
                    elif current_price > r2:
                        signals.append(f"Price above R2 ({r2:.2f}) - Very strong bullish momentum")
                        signals.append("SIGNAL: Overbought zone - Consider profit booking or reversal")
                else:
                    if current_price > s1:
                        signals.append(f"Price below Pivot ({pivot_point:.2f}), above S1 ({s1:.2f})")
                        signals.append("SIGNAL: Potential support at S1 - Watch for bounce")
                    elif current_price < s1 and current_price > s2:
                        signals.append(f"Price below S1 ({s1:.2f}), approaching S2 ({s2:.2f})")
                        signals.append("SIGNAL: Bearish momentum - Watch for S2 support")
                    elif current_price < s2:
                        signals.append(f"Price below S2 ({s2:.2f}) - Very strong bearish momentum")
                        signals.append("SIGNAL: Oversold zone - Consider reversal or further decline")
                
                # Volume analysis
                volume = symbol_data.market_data.volume
                if volume > 100000:
                    signals.append(f"HIGH VOLUME: {volume:,} - Strong interest")
                elif volume < 10000:
                    signals.append(f"LOW VOLUME: {volume:,} - Weak interest")
                
                # Change analysis
                change_pct = symbol_data.market_data.change_percent
                if abs(change_pct) > 5:
                    direction = "UP" if change_pct > 0 else "DOWN"
                    signals.append(f"SIGNIFICANT MOVE: {change_pct:.2f}% {direction}")
                
                # Print signals
                logger.info(f"  Current Price: ₹{current_price:.2f}")
                logger.info(f"  Volume: {volume:,}")
                logger.info(f"  Change: {change_pct:.2f}%")
                logger.info("  Trading Signals:")
                for signal in signals:
                    logger.info(f"    • {signal}")
                
                self.test_results[f"signals_{symbol_data.symbol}"] = True
            
            logger.info(f"✓ Trading signal generation completed")
            return True
            
        except Exception as e:
            logger.error(f"✗ Trading signal generation failed: {e}")
            return False
    
    def generate_comprehensive_report(self):
        """Generate comprehensive report of the pivot point analysis."""
        logger.info("=" * 80)
        logger.info("GENERATING COMPREHENSIVE REPORT")
        logger.info("=" * 80)
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_filename = f"pivot_point_options_test_report_{timestamp}.txt"
            report_path = os.path.join(self.config.output_dir, report_filename)
            
            # Ensure output directory exists
            os.makedirs(self.config.output_dir, exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                # Header
                f.write("=" * 100 + "\n")
                f.write("PIVOT POINT INDICATOR INTEGRATION - OPTIONS TRADING WORKFLOW TEST REPORT\n")
                f.write("=" * 100 + "\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"Test Symbols: {', '.join(self.test_symbols)}\n")
                f.write(f"Pivot Calculation Type: {self.pivot_integration.calculation_type}\n")
                f.write(f"Pivot Integration Enabled: {self.pivot_integration.is_enabled()}\n\n")
                
                # Test Results Summary
                f.write("TEST RESULTS SUMMARY:\n")
                f.write("-" * 50 + "\n")
                passed = sum(1 for result in self.test_results.values() if result)
                total = len(self.test_results)
                f.write(f"Tests Passed: {passed}/{total} ({(passed/total)*100:.1f}%)\n\n")
                
                # Detailed Analysis for each symbol
                for i, symbol_data in enumerate(self.symbol_data, 1):
                    f.write(f"SYMBOL {i}: {symbol_data.symbol}\n")
                    f.write("-" * 50 + "\n")
                    
                    # Basic Information
                    f.write(f"Underlying: {symbol_data.underlying}\n")
                    f.write(f"Option Type: {symbol_data.option_type}\n")
                    f.write(f"Strike Price: ₹{symbol_data.strike_price:.2f}\n")
                    f.write(f"Expiry Date: {symbol_data.expiry_date}\n\n")
                    
                    # Market Data
                    if symbol_data.market_data:
                        md = symbol_data.market_data
                        f.write("MARKET DATA:\n")
                        f.write(f"  LTP: ₹{getattr(md, 'ltp', 0.0):.2f}\n")
                        f.write(f"  Volume: {getattr(md, 'volume', 0):,}\n")
                        f.write(f"  Open: ₹{getattr(md, 'open_price', 0.0):.2f}\n")
                        f.write(f"  High: ₹{getattr(md, 'high_price', 0.0):.2f}\n")
                        f.write(f"  Low: ₹{getattr(md, 'low_price', 0.0):.2f}\n")
                        f.write(f"  Close: ₹{getattr(md, 'close_price', 0.0):.2f}\n")
                        f.write(f"  Prev Close: ₹{getattr(md, 'prev_close', 0.0):.2f}\n")
                        f.write(f"  Change: ₹{getattr(md, 'change', 0.0):.2f} ({getattr(md, 'change_percent', 0.0):.2f}%)\n\n")
                    
                    # Pivot Point Analysis
                    if symbol_data.pivot_data:
                        pd = symbol_data.pivot_data
                        f.write("PIVOT POINT ANALYSIS:\n")
                        f.write("  Pivot Levels:\n")
                        for level, value in pd.pivot_levels.items():
                            f.write(f"    {level}: ₹{value:.2f}\n")
                        
                        f.write("\n  Key Pivot Information:\n")
                        if pd.closest_positive_pivot_level:
                            f.write(f"    Closest Positive Pivot: {pd.closest_positive_pivot_level} = ₹{pd.closest_positive_pivot_value:.2f}\n")
                            f.write(f"    Distance: ₹{pd.distance_to_positive_pivot:.2f} ({pd.distance_to_positive_pivot_pct:.2f}%)\n")
                        
                        if pd.min_positive_pivot_level:
                            f.write(f"    Min Positive Pivot: {pd.min_positive_pivot_level} = ₹{pd.min_positive_pivot_value:.2f}\n")
                            f.write(f"    Distance: ₹{pd.distance_to_min_positive_pivot:.2f} ({pd.distance_to_min_positive_pivot_pct:.2f}%)\n")
                    
                    f.write("\n" + "=" * 100 + "\n\n")
                
                # Configuration Details
                f.write("CONFIGURATION USED:\n")
                f.write("-" * 50 + "\n")
                f.write(f"Pivot Point Enabled: {self.config.pivot_point_enabled}\n")
                f.write(f"Calculation Type: {self.config.pivot_point_calculation_type}\n")
                f.write(f"Top N Closest: {self.config.pivot_point_top_n_closest}\n")
                f.write(f"Market Types: {self.config.get_enabled_market_types()}\n")
                f.write(f"Volume Filter: {self.config.min_volume:,} - {self.config.max_volume:,}\n")
                f.write(f"LTP Filter: ₹{self.config.min_ltp_price:.2f} - ₹{self.config.max_ltp_price:.2f}\n\n")
                
                # Test Results Details
                f.write("DETAILED TEST RESULTS:\n")
                f.write("-" * 50 + "\n")
                for test_name, result in self.test_results.items():
                    status = "PASS" if result else "FAIL"
                    f.write(f"{test_name}: {status}\n")
                
                f.write("\n" + "=" * 100 + "\n")
                f.write("END OF REPORT\n")
                f.write("=" * 100 + "\n")
            
            logger.info(f"✓ Comprehensive report generated: {report_path}")
            
            # Also print summary to console
            self.print_console_summary()
            
            return report_path
            
        except Exception as e:
            logger.error(f"✗ Report generation failed: {e}")
            return None
    
    def print_console_summary(self):
        """Print a summary of results to console."""
        logger.info("=" * 80)
        logger.info("PIVOT POINT OPTIONS WORKFLOW TEST - CONSOLE SUMMARY")
        logger.info("=" * 80)
        
        # Test Results Summary
        passed = sum(1 for result in self.test_results.values() if result)
        total = len(self.test_results)
        logger.info(f"Overall Test Results: {passed}/{total} tests passed ({(passed/total)*100:.1f}%)")
        
        # Symbol Analysis Summary
        logger.info(f"\nSymbol Analysis Summary:")
        logger.info(f"Total Symbols Tested: {len(self.symbol_data)}")
        
        for symbol_data in self.symbol_data:
            logger.info(f"\n{symbol_data.symbol}:")
            logger.info(f"  ├─ Underlying: {symbol_data.underlying}")
            logger.info(f"  ├─ Type: {symbol_data.option_type}")
            logger.info(f"  ├─ Strike: ₹{symbol_data.strike_price:.2f}")
            
            if symbol_data.market_data:
                logger.info(f"  ├─ LTP: ₹{symbol_data.market_data.ltp:.2f}")
                logger.info(f"  ├─ Volume: {symbol_data.market_data.volume:,}")
                logger.info(f"  ├─ Change: {symbol_data.market_data.change_percent:.2f}%")
            
            if symbol_data.pivot_data:
                pivot = symbol_data.pivot_data.pivot_levels.get('Pivot', 0)
                r1 = symbol_data.pivot_data.pivot_levels.get('R1', 0)
                s1 = symbol_data.pivot_data.pivot_levels.get('S1', 0)
                logger.info(f"  ├─ Pivot: ₹{pivot:.2f}")
                logger.info(f"  ├─ R1: ₹{r1:.2f}")
                logger.info(f"  └─ S1: ₹{s1:.2f}")
                
                if symbol_data.pivot_data.closest_positive_pivot_level:
                    logger.info(f"     └─ Next Target: {symbol_data.pivot_data.closest_positive_pivot_level} @ ₹{symbol_data.pivot_data.closest_positive_pivot_value:.2f}")
        
        # Key Insights
        logger.info(f"\nKey Insights:")
        logger.info(f"  • Pivot point integration is {'WORKING' if self.pivot_integration.is_enabled() else 'DISABLED'}")
        logger.info(f"  • Calculation type: {self.pivot_integration.calculation_type}")
        logger.info(f"  • All symbols successfully processed with pivot analysis")
        logger.info(f"  • Trading signals generated based on pivot levels")
        
        if passed == total:
            logger.info(f"\n🎉 ALL TESTS PASSED! Pivot point integration is working correctly.")
        else:
            logger.warning(f"\n⚠️ {total - passed} tests failed. Please review the issues above.")
        
        logger.info("=" * 80)
    
    def run_complete_workflow_test(self):
        """Run the complete pivot point options workflow test."""
        logger.info("=" * 80)
        logger.info("STARTING PIVOT POINT OPTIONS WORKFLOW TEST")
        logger.info("=" * 80)
        logger.info(f"Test Symbols: {', '.join(self.test_symbols)}")
        logger.info(f"Start Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # Step 1: Setup test environment
            if not self.setup_test_environment():
                logger.error("✗ Test environment setup failed")
                return False
            
            # Step 2: Parse test symbols
            if not self.parse_test_symbols():
                logger.error("✗ Symbol parsing failed")
                return False
            
            # Step 3: Fetch market data
            if not self.fetch_market_data():
                logger.error("✗ Market data fetching failed")
                return False
            
            # Step 4: Calculate pivot points
            if not self.calculate_pivot_points():
                logger.error("✗ Pivot point calculation failed")
                return False
            
            # Step 5: Generate trading signals
            if not self.generate_trading_signals():
                logger.error("✗ Trading signal generation failed")
                return False
            
            # Step 6: Generate comprehensive report
            report_path = self.generate_comprehensive_report()
            if not report_path:
                logger.error("✗ Report generation failed")
                return False
            
            # Final summary
            logger.info("=" * 80)
            logger.info("PIVOT POINT OPTIONS WORKFLOW TEST COMPLETED SUCCESSFULLY")
            logger.info("=" * 80)
            logger.info(f"End Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"Report Generated: {report_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"✗ Workflow test failed: {e}")
            return False

def main():
    """Main test function."""
    test = PivotPointOptionsWorkflowTest()
    success = test.run_complete_workflow_test()
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)