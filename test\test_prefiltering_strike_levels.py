"""
Test to ensure prefiltering of symbols strike levels works correctly.
This test verifies that only symbols within the configured strike levels are retrieved
instead of all symbols.
"""

import unittest
import yaml
import tempfile
import os
from typing import List, Dict
from datetime import datetime
import logging

from config_loader import ConfigLoader
from options_chain_filter import OptionsChainFilter
from universal_symbol_parser import UniversalSymbolParser, UniversalSymbol

# Setup logging for test
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestPrefilteringStrikeLevels(unittest.TestCase):
    """Test prefiltering of options symbols based on strike levels configuration."""

    def setUp(self):
        """Set up test fixtures."""
        # Create test config with specific strike levels
        self.test_config = {
            'symbols': ['NIFTY', 'BANKNIFTY'],
            'csv_files': {
                'equity': 'NSE_CM.csv',
                'index': 'NSE_CM.csv',
                'futures': 'NSE_FO.csv',
                'options': 'NSE_FO.csv'
            },
            'volume_filter': {
                'enabled': True,
                'min_volume': 1000,
                'max_volume': ********
            },
            'ltp_filter': {
                'enabled': True,
                'min_ltp_price': 1.0,
                'max_ltp_price': 1000.0
            },
            'timeframe': {
                'interval': 15,
                'days_to_fetch': 2
            },
            'options_filter': {
                'enabled': True,
                'strike_level': 10,  # Test with 10 strike levels
                'expiry_months': 3
            },
            'ce_pe_pairing': {
                'enabled': True,
                'min_price_percent': 5.0,
                'max_price_percent': 50.0
            },
            'mae_indicator': {
                'enabled': False  # Disable MAE for simpler testing
            },
            'market_types': {
                'equity': False,
                'index': False,
                'futures': False,
                'options': True
            },
            'output_dir': 'test_reports'
        }

        # Create temporary config file
        self.temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(self.test_config, self.temp_config_file)
        self.temp_config_file.close()

        # Initialize config loader
        self.config = ConfigLoader(self.temp_config_file.name)
        
        # Initialize options chain filter
        self.options_filter = OptionsChainFilter(self.config)
        
        # Initialize symbol parser
        self.symbol_parser = UniversalSymbolParser(self.config, self.config.symbols)

    def tearDown(self):
        """Clean up test fixtures."""
        os.unlink(self.temp_config_file.name)

    def create_mock_options_symbols(self, underlying: str, spot_price: float) -> List[UniversalSymbol]:
        """Create mock options symbols for testing."""
        symbols = []
        current_date = datetime.now()
        
        # Create symbols for current month
        year = str(current_date.year)[-2:]
        month = current_date.strftime('%b').upper()
        
        # Create strikes around spot price
        strike_interval = 50 if underlying == 'NIFTY' else 100
        
        # Create 30 strike levels (15 above, 15 below spot)
        for i in range(-15, 16):
            strike = int(spot_price + (i * strike_interval))
            if strike > 0:
                # Create CE symbol
                ce_symbol_str = f"{underlying}{year}{month}{strike}CE"
                ce_symbol = UniversalSymbol(
                    symbol=ce_symbol_str,
                    market_type='OPTIONS',
                    underlying=underlying,
                    expiry_year=year,
                    expiry_month=month,
                    strike_price=strike,
                    option_type='CE'
                )
                symbols.append(ce_symbol)
                
                # Create PE symbol
                pe_symbol_str = f"{underlying}{year}{month}{strike}PE"
                pe_symbol = UniversalSymbol(
                    symbol=pe_symbol_str,
                    market_type='OPTIONS',
                    underlying=underlying,
                    expiry_year=year,
                    expiry_month=month,
                    strike_price=strike,
                    option_type='PE'
                )
                symbols.append(pe_symbol)
        
        logger.info(f"Created {len(symbols)} mock options symbols for {underlying}")
        return symbols

    def test_prefiltering_reduces_symbols_correctly(self):
        """Test that prefiltering reduces symbols to configured strike levels."""
        # Mock spot prices
        spot_prices = {
            'NIFTY': 25000.0,
            'BANKNIFTY': 52000.0
        }
        
        # Create mock options symbols
        nifty_symbols = self.create_mock_options_symbols('NIFTY', 25000.0)
        banknifty_symbols = self.create_mock_options_symbols('BANKNIFTY', 52000.0)
        all_symbols = nifty_symbols + banknifty_symbols
        
        logger.info(f"Total mock symbols created: {len(all_symbols)}")
        
        # Apply prefiltering
        filtered_symbols = self.options_filter.pre_filter_options_symbols(
            all_symbols, 
            spot_prices, 
            max_symbols_per_underlying=200
        )
        
        logger.info(f"Symbols after prefiltering: {len(filtered_symbols)}")
        
        # Verify filtering worked
        self.assertLess(len(filtered_symbols), len(all_symbols), 
                       "Prefiltering should reduce the number of symbols")
        
        # Verify that only symbols within strike levels are included
        for symbol in filtered_symbols:
            underlying = symbol.underlying
            spot_price = spot_prices[underlying]
            strike_interval = 50 if underlying == 'NIFTY' else 100
            
            # Calculate expected strike range
            strike_range = self.config.options_strike_level * strike_interval
            min_expected_strike = spot_price - strike_range
            max_expected_strike = spot_price + strike_range
            
            self.assertGreaterEqual(symbol.strike_price, min_expected_strike,
                                  f"Strike {symbol.strike_price} should be >= {min_expected_strike}")
            self.assertLessEqual(symbol.strike_price, max_expected_strike,
                               f"Strike {symbol.strike_price} should be <= {max_expected_strike}")

    def test_prefiltering_preserves_ce_pe_balance(self):
        """Test that prefiltering preserves CE/PE balance."""
        # Mock spot prices
        spot_prices = {
            'NIFTY': 25000.0,
            'BANKNIFTY': 52000.0
        }
        
        # Create mock options symbols
        nifty_symbols = self.create_mock_options_symbols('NIFTY', 25000.0)
        banknifty_symbols = self.create_mock_options_symbols('BANKNIFTY', 52000.0)
        all_symbols = nifty_symbols + banknifty_symbols
        
        # Apply prefiltering
        filtered_symbols = self.options_filter.pre_filter_options_symbols(
            all_symbols, 
            spot_prices, 
            max_symbols_per_underlying=200
        )
        
        # Count CE and PE symbols
        ce_count = sum(1 for s in filtered_symbols if s.option_type == 'CE')
        pe_count = sum(1 for s in filtered_symbols if s.option_type == 'PE')
        
        logger.info(f"CE symbols: {ce_count}, PE symbols: {pe_count}")
        
        # Verify CE/PE balance (should be roughly equal)
        self.assertGreater(ce_count, 0, "Should have CE symbols")
        self.assertGreater(pe_count, 0, "Should have PE symbols")
        
        # Allow for small differences due to filtering
        ratio = ce_count / pe_count if pe_count > 0 else 0
        self.assertGreater(ratio, 0.8, "CE/PE ratio should be balanced")
        self.assertLess(ratio, 1.2, "CE/PE ratio should be balanced")

    def test_prefiltering_respects_strike_level_config(self):
        """Test that prefiltering respects the configured strike level."""
        # Test with different strike levels
        test_strike_levels = [5, 10, 15]
        
        for strike_level in test_strike_levels:
            # Update config by modifying the underlying config
            self.test_config['options_filter']['strike_level'] = strike_level
            
            # Create new config file with updated strike level
            temp_config_file = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
            yaml.dump(self.test_config, temp_config_file)
            temp_config_file.close()
            
            # Load new config
            config = ConfigLoader(temp_config_file.name)
            options_filter = OptionsChainFilter(config)
            
            # Mock spot prices
            spot_prices = {'NIFTY': 25000.0}
            
            # Create mock symbols
            nifty_symbols = self.create_mock_options_symbols('NIFTY', 25000.0)
            
            # Apply prefiltering
            filtered_symbols = options_filter.pre_filter_options_symbols(
                nifty_symbols, 
                spot_prices, 
                max_symbols_per_underlying=200
            )
            
            # Calculate expected max symbols based on strike level
            # Each strike level should have 2 symbols (CE + PE)
            expected_max_symbols = strike_level * 2 * 2  # 2 for CE/PE, 2 for both sides of ATM
            
            logger.info(f"Strike level {strike_level}: {len(filtered_symbols)} symbols "
                       f"(expected max: {expected_max_symbols})")
            
            # Verify symbols are within expected range
            self.assertLessEqual(len(filtered_symbols), expected_max_symbols * 1.5,
                               f"Too many symbols for strike level {strike_level}")
            
            # Clean up temp file
            os.unlink(temp_config_file.name)

    def test_prefiltering_handles_no_spot_price(self):
        """Test that prefiltering handles cases where no spot price is available."""
        # Create mock symbols
        nifty_symbols = self.create_mock_options_symbols('NIFTY', 25000.0)
        
        # Apply prefiltering without spot prices
        filtered_symbols = self.options_filter.pre_filter_options_symbols(
            nifty_symbols, 
            spot_prices=None,  # No spot prices
            max_symbols_per_underlying=100
        )
        
        # Should still filter symbols (using fallback method)
        self.assertLess(len(filtered_symbols), len(nifty_symbols),
                       "Should filter symbols even without spot prices")
        self.assertGreater(len(filtered_symbols), 0,
                          "Should return some symbols")

    def test_prefiltering_with_empty_symbols(self):
        """Test that prefiltering handles empty symbol lists."""
        # Test with empty list
        filtered_symbols = self.options_filter.pre_filter_options_symbols(
            [], 
            spot_prices={'NIFTY': 25000.0},
            max_symbols_per_underlying=100
        )
        
        self.assertEqual(len(filtered_symbols), 0, "Should return empty list for empty input")

    def run_integration_test(self):
        """Run an integration test that mimics the actual workflow."""
        logger.info("Running integration test for prefiltering...")
        
        try:
            # Check if CSV files exist
            if not os.path.exists('NSE_FO.csv'):
                logger.warning("NSE_FO.csv not found, skipping integration test")
                return
            
            # Load real symbols from CSV
            symbols_to_scan = self.symbol_parser.get_symbols_for_market_type('OPTIONS', ['NIFTY'])
            
            if not symbols_to_scan:
                logger.warning("No NIFTY options symbols found, skipping integration test")
                return
            
            logger.info(f"Loaded {len(symbols_to_scan)} NIFTY options symbols from CSV")
            
            # Parse symbols
            parsed_symbols = []
            for symbol in symbols_to_scan[:1000]:  # Limit to first 1000 for testing
                parsed_symbol = self.symbol_parser.parse_symbol(symbol, 'NSE_FO.csv')
                if parsed_symbol and parsed_symbol.is_options():
                    parsed_symbols.append(parsed_symbol)
            
            logger.info(f"Parsed {len(parsed_symbols)} valid options symbols")
            
            if not parsed_symbols:
                logger.warning("No valid parsed symbols, skipping integration test")
                return
            
            # Apply prefiltering
            filtered_symbols = self.options_filter.pre_filter_options_symbols(
                parsed_symbols, 
                spot_prices={'NIFTY': 25000.0},
                max_symbols_per_underlying=150
            )
            
            logger.info(f"Prefiltering result: {len(filtered_symbols)}/{len(parsed_symbols)} symbols selected")
            
            # Verify results
            self.assertLess(len(filtered_symbols), len(parsed_symbols),
                           "Prefiltering should reduce symbols")
            self.assertGreater(len(filtered_symbols), 0,
                              "Should return some symbols")
            
            # Verify all returned symbols are within strike range
            for symbol in filtered_symbols:
                self.assertIsNotNone(symbol.strike_price, "Symbol should have strike price")
                self.assertIn(symbol.option_type, ['CE', 'PE'], "Symbol should be CE or PE")
            
            logger.info("Integration test passed!")
            
        except Exception as e:
            logger.error(f"Integration test failed: {e}")
            raise


def main():
    """Run the prefiltering tests."""
    logger.info("Starting prefiltering strike levels tests...")
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestPrefilteringStrikeLevels)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Run integration test if unit tests pass
    if result.wasSuccessful():
        logger.info("\nRunning integration test...")
        test_instance = TestPrefilteringStrikeLevels()
        test_instance.setUp()
        try:
            test_instance.run_integration_test()
            logger.info("All tests passed!")
        except Exception as e:
            logger.error(f"Integration test failed: {e}")
        finally:
            test_instance.tearDown()
    else:
        logger.error("Unit tests failed, skipping integration test")
    
    return result.wasSuccessful()


if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
