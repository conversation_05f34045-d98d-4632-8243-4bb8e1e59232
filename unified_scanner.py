"""
Unified scanner that handles all market types (EQUITY, INDEX, FUTURES, OPTIONS).
Processes each market type sequentially and generates separate reports for each.
"""

import logging
import sys
import os
import subprocess
from datetime import datetime
from typing import Dict, Any, List

# Import our modules
from fyers_config import setup_logging
from config_loader import get_config
from market_type_scanner import MarketTypeScannerFactory, FilteredSymbol
from report_generator import ReportGenerator

logger = logging.getLogger(__name__)

class UnifiedScanner:
    """Unified scanner that orchestrates all market type scanning."""

    def __init__(self, config_path: str = "config.yaml"):
        """
        Initialize the unified scanner.

        Args:
            config_path: Path to the configuration file
        """
        self.config = get_config(config_path)
        self.enabled_market_types = self.config.get_enabled_market_types()

        # Initialize results structure for all market types
        self.results = {}
        for market_type in self.enabled_market_types:
            self.results[market_type.lower()] = {
                'symbols': [],
                'summary': {},
                'reports': {}
            }

        logger.info(f"Unified scanner initialized for market types: {self.enabled_market_types}")
        
    def validate_prerequisites(self) -> bool:
        """
        Validate that all required files and dependencies are available.

        Returns:
            True if all prerequisites are met, False otherwise
        """
        logger.info("Validating prerequisites...")

        # Validate configuration
        if not self.config.validate_config():
            logger.error("Configuration validation failed. Please check config.yaml")
            return False

        # Check if required CSV files exist (they will be downloaded if not)
        required_csv_files = set()
        for market_type in self.enabled_market_types:
            csv_file = self.config.get_csv_file_for_market_type(market_type)
            required_csv_files.add(csv_file)

        missing_files = []
        for file in required_csv_files:
            if not os.path.exists(file):
                missing_files.append(file)

        if missing_files:
            logger.info(f"Missing CSV files will be downloaded: {', '.join(missing_files)}")

        try:
            # Check if required packages are available
            import yaml
            import fyers_apiv3
            logger.info("All prerequisites satisfied")
            return True
        except ImportError as e:
            logger.error(f"Missing required package: {e}")
            logger.error("Please install required packages using: pip install pyyaml fyers-apiv3")
            return False
    
    def download_symbol_files(self) -> bool:
        """
        Download the latest symbol files from configured URLs.
        
        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("Downloading latest symbol files...")
            subprocess.run(["python", "symbol_downloader.py"], check=True)
            logger.info("Symbol files downloaded successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to download symbol files: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during symbol file download: {e}")
            return False
    
    def scan_market_type(self, market_type: str) -> bool:
        """
        Scan symbols for a specific market type.

        Args:
            market_type: Market type to scan (EQUITY, INDEX, FUTURES, OPTIONS)

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("=" * 60)
            logger.info(f"STARTING {market_type} SCANNING")
            logger.info("=" * 60)

            # Check if required CSV file exists
            csv_file = self.config.get_csv_file_for_market_type(market_type)
            if not os.path.exists(csv_file):
                logger.warning(f"{csv_file} not found. Skipping {market_type} scanning.")
                return True

            # Create scanner for this market type
            scanner = MarketTypeScannerFactory.create_scanner(market_type, self.config)

            # Run scan
            filtered_symbols = scanner.scan_symbols()

            if not filtered_symbols:
                logger.warning(f"No {market_type} symbols found matching the criteria")
                self.results[market_type.lower()]['symbols'] = []
                self.results[market_type.lower()]['summary'] = {'total_symbols': 0}
                return True

            # Generate summary statistics
            summary_stats = self.get_scan_summary(filtered_symbols, market_type)

            # Generate reports
            report_generator = ReportGenerator(
                output_dir=self.config.output_dir,
                config=self.config
            )

            report_files = report_generator.generate_market_type_report(
                filtered_symbols,
                summary_stats,
                market_type
            )

            # Store results
            self.results[market_type.lower()]['symbols'] = filtered_symbols
            self.results[market_type.lower()]['summary'] = summary_stats
            self.results[market_type.lower()]['reports'] = report_files

            logger.info("=" * 60)
            logger.info(f"{market_type} SCANNING COMPLETED")
            logger.info("=" * 60)
            logger.info(f"{market_type} symbols found: {len(filtered_symbols)}")
            logger.info(f"{market_type} CSV Report: {report_files.get('csv_report', 'N/A')}")
            logger.info(f"{market_type} Summary Report: {report_files.get('summary_report', 'N/A')}")

            return True

        except Exception as e:
            logger.error(f"Error during {market_type} scanning: {e}", exc_info=True)
            return False
    
    def get_scan_summary(self, filtered_symbols: List[FilteredSymbol], market_type: str) -> Dict[str, Any]:
        """
        Generate summary statistics for filtered symbols.

        Args:
            filtered_symbols: List of filtered symbols
            market_type: Market type

        Returns:
            Summary statistics dictionary
        """
        if not filtered_symbols:
            return {'total_symbols': 0}

        # Basic statistics
        total_symbols = len(filtered_symbols)
        unique_underlyings = len(set(s.underlying for s in filtered_symbols))

        # Market data statistics
        volumes = [s.market_data.volume for s in filtered_symbols]
        ltps = [s.market_data.ltp for s in filtered_symbols]

        avg_volume = sum(volumes) / len(volumes) if volumes else 0
        avg_ltp = sum(ltps) / len(ltps) if ltps else 0.0

        summary = {
            'total_symbols': total_symbols,
            'unique_underlyings': unique_underlyings,
            'avg_volume': avg_volume,
            'avg_ltp': avg_ltp,
            'market_type': market_type
        }

        # Market type specific statistics
        if market_type == 'OPTIONS':
            ce_count = sum(1 for s in filtered_symbols if s.option_type == 'CE')
            pe_count = sum(1 for s in filtered_symbols if s.option_type == 'PE')
            summary.update({
                'ce_options': ce_count,
                'pe_options': pe_count
            })

        return summary
    
    def generate_combined_summary(self) -> str:
        """
        Generate a combined summary of all market type scanning results.

        Returns:
            Path to the combined summary file
        """
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"combined_scan_summary_{timestamp}.txt"
            filepath = os.path.join(self.config.output_dir, filename)

            with open(filepath, 'w', encoding='utf-8') as f:
                # Header
                f.write("=" * 80 + "\n")
                f.write("UNIFIED SCANNER COMBINED SUMMARY REPORT\n")
                f.write("=" * 80 + "\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

                # Overall Statistics
                total_count = 0
                market_counts = {}

                for market_type in self.enabled_market_types:
                    count = self.results[market_type.lower()]['summary'].get('total_symbols', 0)
                    market_counts[market_type] = count
                    total_count += count

                f.write("OVERALL STATISTICS:\n")
                f.write("-" * 40 + "\n")
                f.write(f"Total Symbols Found: {total_count}\n")
                for market_type, count in market_counts.items():
                    f.write(f"  - {market_type} Symbols: {count}\n")
                f.write("\n")

                # Individual Market Type Results
                for market_type in self.enabled_market_types:
                    market_key = market_type.lower()
                    count = market_counts[market_type]
                    csv_file = self.config.get_csv_file_for_market_type(market_type)

                    f.write(f"{market_type} SCANNING RESULTS ({csv_file}):\n")
                    f.write("-" * 40 + "\n")

                    if count > 0:
                        summary = self.results[market_key]['summary']
                        f.write(f"Symbols Found: {count}\n")
                        f.write(f"Unique Underlyings: {summary.get('unique_underlyings', 0)}\n")
                        f.write(f"Average Volume: {summary.get('avg_volume', 0):,}\n")
                        f.write(f"Average LTP: ₹{summary.get('avg_ltp', 0.0):.2f}\n")

                        # Options-specific statistics
                        if market_type == 'OPTIONS':
                            f.write(f"CE Options: {summary.get('ce_options', 0)}\n")
                            f.write(f"PE Options: {summary.get('pe_options', 0)}\n")

                        if 'reports' in self.results[market_key]:
                            reports = self.results[market_key]['reports']
                            f.write(f"CSV Report: {os.path.basename(reports.get('csv_report', 'N/A'))}\n")
                            f.write(f"Summary Report: {os.path.basename(reports.get('summary_report', 'N/A'))}\n")
                    else:
                        f.write(f"No {market_type.lower()} symbols found matching criteria\n")
                    f.write("\n")

                # Configuration Used
                f.write("CONFIGURATION USED:\n")
                f.write("-" * 40 + "\n")
                f.write(f"Market Types: {', '.join(self.enabled_market_types)}\n")
                f.write(f"Target Symbols: {', '.join(self.config.symbols)}\n")
                f.write(f"Volume Filter: {self.config.min_volume:,} - {self.config.max_volume:,}\n")
                f.write(f"LTP Filter: ₹{self.config.min_ltp_price:.2f} - ₹{self.config.max_ltp_price:.2f}\n")
                f.write(f"Options Strike Level: {self.config.options_strike_level}\n")
                f.write("\n")

                f.write("=" * 80 + "\n")
                f.write("End of Combined Report\n")
                f.write("=" * 80 + "\n")

            logger.info(f"Combined summary report generated: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"Error generating combined summary: {e}")
            raise
    
    def run_unified_scan(self) -> bool:
        """
        Run the complete unified scanning process for all market types.

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info("=" * 80)
            logger.info("UNIFIED SCANNER APPLICATION STARTED")
            logger.info("=" * 80)
            logger.info(f"Start time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"Enabled market types: {', '.join(self.enabled_market_types)}")

            # Step 1: Validate prerequisites
            if not self.validate_prerequisites():
                return False

            # Step 2: Download symbol files
            if not self.download_symbol_files():
                return False

            # Step 3: Scan each market type sequentially
            for market_type in self.enabled_market_types:
                if not self.scan_market_type(market_type):
                    logger.error(f"{market_type} scanning failed")
                    return False

            # Step 4: Generate combined summary
            combined_summary_path = self.generate_combined_summary()

            # Step 5: Log completion
            logger.info("=" * 80)
            logger.info("UNIFIED SCANNER COMPLETED SUCCESSFULLY")
            logger.info("=" * 80)
            logger.info(f"End time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # Calculate totals
            total_count = 0
            market_counts = {}

            for market_type in self.enabled_market_types:
                count = len(self.results[market_type.lower()]['symbols'])
                market_counts[market_type] = count
                total_count += count

            logger.info(f"Total symbols found: {total_count}")
            for market_type, count in market_counts.items():
                logger.info(f"  - {market_type} symbols: {count}")
            logger.info(f"Combined summary: {combined_summary_path}")

            # Print final message to console
            print(f"\nUnified scanning completed successfully!")
            print(f"Total symbols found: {total_count}")
            for market_type, count in market_counts.items():
                print(f"  - {market_type} symbols: {count}")
            print(f"Reports saved to: {self.config.output_dir}")

            # Print individual report files
            for market_type in self.enabled_market_types:
                market_key = market_type.lower()
                count = market_counts[market_type]
                if count > 0 and 'reports' in self.results[market_key]:
                    reports = self.results[market_key]['reports']
                    print(f"   - {market_type} CSV: {os.path.basename(reports.get('csv_report', 'N/A'))}")
                    print(f"   - {market_type} Summary: {os.path.basename(reports.get('summary_report', 'N/A'))}")

            print(f"   - Combined Summary: {os.path.basename(combined_summary_path)}")

            return True

        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
            print("\nApplication interrupted by user")
            return False
        except Exception as e:
            logger.error(f"Unified scanner failed with error: {e}", exc_info=True)
            print(f"\nUnified scanner failed: {e}")
            print("Check the log files for detailed error information")
            return False

def main():
    """Main function for unified scanner."""
    # Setup logging
    logger = setup_logging(level=logging.INFO, log_to_file=True)
    
    # Change working directory to the script's directory
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # Initialize and run unified scanner
    scanner = UnifiedScanner()
    success = scanner.run_unified_scan()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()