# Multi-Market Scanner Agent Guidelines

## Commands
- **Main Application**: `python main.py`
- **Run Tests**: `python test_comprehensive.py` (full test suite)
- **Run Single Test**: `python test_main_functionality.py` or `python test_options_prefiltering.py`
- **Install Dependencies**: `pip install pyyaml fyers-apiv3 pandas ta`
- **Check Prerequisites**: Check config.yaml and .env file exist
- **Download Symbols**: `python symbol_downloader.py`

## Architecture
- **Entry Point**: main.py - orchestrates the unified scanning process
- **Core Scanner**: unified_scanner.py - handles all market types (EQUITY, INDEX, FUTURES, OPTIONS)
- **Configuration**: config_loader.py - loads settings from config.yaml
- **Symbol Parsing**: universal_symbol_parser.py - unified parser for all market types
- **Market Processing**: market_type_scanner.py - market-specific scanning logic
- **API Client**: fyers_client.py - handles Fyers API v3 integration with rate limiting
- **Reports**: report_generator.py - generates CSV and summary reports per market type
- **Output**: reports/ directory with timestamped CSV files and summary reports

## Code Style
- **Imports**: Standard library first, then third-party, then local modules
- **Logging**: Use logger from fyers_config.setup_logging() with INFO level
- **Error Handling**: Comprehensive try-catch with detailed logging and graceful degradation
- **Type Hints**: Use typing module for function signatures and return types
- **Naming**: snake_case for variables/functions, PascalCase for classes
- **Config**: All settings in config.yaml with validation in config_loader.py
- **API Calls**: Use rate limiting (0.1s delay) and retry logic (5 retries with exponential backoff)
- **Data**: Process in batches, handle large datasets efficiently
- **File Paths**: Use os.path.join for cross-platform compatibility
