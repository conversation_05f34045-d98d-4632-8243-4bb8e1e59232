[CmdletBinding()]
param (
    [Parameter(Mandatory=$true, Position=0)]
    [string]$Time
)

# Define your task details here 24 hour format
# Example: '18:30'
$taskName = "Aall_Market_Scanner_9_09"
$scriptDir = $PSScriptRoot
$pythonPath = "python.exe"
$scriptPath = "main.py"

Write-Host "Creating/Updating scheduled task: $taskName"

$action = New-ScheduledTaskAction -Execute $pythonPath -Argument $scriptPath -WorkingDirectory $scriptDir
$trigger = New-ScheduledTaskTrigger -Daily -At $Time

$settings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -StartWhenAvailable -DontStopOnIdleEnd

try {
    Register-ScheduledTask -TaskName $taskName -Action $action -Trigger $trigger -Settings $settings -Force -User $env:USERNAME
    Write-Host "Successfully created task '$taskName' to run daily at $Time."
    Write-Host "To verify, open Task Scheduler and look for it in the main library."
} catch {
    Write-Error "Failed to create the scheduled task. Error: $_"
    Write-Error "Please make sure you are running this script with sufficient privileges (e.g., as Administrator)."
}
